#!/usr/bin/env python3

print("Starting simple test...")

# Test basic Python functionality
print("✅ Python is working")

# Test basic imports
try:
    import os
    print("✅ os imported")
    
    from dataclasses import dataclass
    print("✅ dataclass imported")
    
    from enum import Enum
    print("✅ Enum imported")
    
    from typing import Dict, Any
    print("✅ typing imported")
    
except Exception as e:
    print(f"❌ Basic import error: {e}")
    exit(1)

# Test our enum
try:
    class TestEnum(Enum):
        TEST = "test"
    
    print("✅ Enum creation works")
except Exception as e:
    print(f"❌ Enum creation error: {e}")
    exit(1)

# Test dataclass
try:
    @dataclass
    class TestClass:
        value: str = "test"
    
    obj = TestClass()
    print(f"✅ Dataclass creation works: {obj.value}")
except Exception as e:
    print(f"❌ Dataclass creation error: {e}")
    exit(1)

print("All basic tests passed!")
