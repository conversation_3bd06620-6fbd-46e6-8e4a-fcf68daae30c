#!/usr/bin/env python3
"""
Enhanced Intelligent Highlights Extractor

Advanced highlights extraction with sophisticated engagement scoring integration.
Maintains backward compatibility while providing enhanced scoring capabilities.
"""

import os
import time
import logging
from typing import Dict, Any, List, Optional, Tuple

from .intelligent_extractor import IntelligentHighlightsExtractor
from .engagement_scorer import EngagementScorer
from ..models.engagement_models import ScoringConfig, EngagementScore
from ..config.engagement_settings import (
    ENGAGEMENT_SCORING_ENABLED, ENGAGEMENT_SCORING_FALLBACK_TO_BASIC
)


class EnhancedIntelligentExtractor(IntelligentHighlightsExtractor):
    """
    Enhanced version of the intelligent highlights extractor with sophisticated engagement scoring.
    
    Features:
    - Advanced emotion detection using GoEmotions/DistilRoBERTa
    - Rhetorical analysis using spaCy and pattern matching
    - Topic transition detection using BERTopic
    - Keyword significance using BERT embeddings
    - GPU acceleration with CPU fallback
    - Comprehensive caching and performance optimization
    - Backward compatibility with existing pipeline
    """
    
    def __init__(self, config: Optional[ScoringConfig] = None):
        """
        Initialize the enhanced extractor
        
        Args:
            config: Optional engagement scoring configuration
        """
        # Initialize parent class
        super().__init__()
        
        self.logger = logging.getLogger(__name__)
        self.engagement_scorer = None
        self.scoring_config = config or ScoringConfig.from_env()
        
        # Initialize engagement scoring if enabled
        if ENGAGEMENT_SCORING_ENABLED:
            try:
                self.engagement_scorer = EngagementScorer(self.scoring_config)
                self.logger.info("Enhanced engagement scoring initialized successfully")
            except Exception as e:
                self.logger.error(f"Failed to initialize engagement scoring: {str(e)}")
                if not ENGAGEMENT_SCORING_FALLBACK_TO_BASIC:
                    raise
                self.logger.warning("Falling back to basic scoring")
                self.engagement_scorer = None
        else:
            self.logger.info("Enhanced engagement scoring disabled")
    
    def extract(self, transcription_data: Dict[str, Any], video_path: str,
                keywords: List[str], target_length: int = 75,
                output_dir: str = "output", params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Extract highlights with enhanced engagement scoring
        
        Args:
            transcription_data: Transcription data with segments
            video_path: Path to the video file
            keywords: Keywords for significance scoring
            target_length: Target total duration in seconds
            output_dir: Output directory for results
            params: Additional parameters
            
        Returns:
            Enhanced extraction results with engagement metrics
        """
        start_time = time.time()
        self.logger.info("🚀 Starting enhanced highlights extraction")
        
        # Get segments from transcription data
        segments = transcription_data.get('segments', [])
        if not segments:
            raise ValueError("No segments found in transcription data")
        
        self.logger.info(f"Processing {len(segments)} transcript segments")
        
        # Enhanced engagement scoring (if available)
        engagement_scores = []
        if self.engagement_scorer:
            try:
                self.logger.info("🧠 Computing sophisticated engagement scores")
                engagement_scores = self.engagement_scorer.score_segments(segments, keywords)
                self.logger.info(f"✨ Generated {len(engagement_scores)} engagement scores")
                
                # Analyze engagement distribution
                engagement_analysis = self.engagement_scorer.analyze_engagement_distribution(engagement_scores)
                self.logger.info(f"📊 Engagement analysis: {engagement_analysis.get('high_engagement_count', 0)} high-engagement segments")
                
            except Exception as e:
                self.logger.error(f"Enhanced engagement scoring failed: {str(e)}")
                if not ENGAGEMENT_SCORING_FALLBACK_TO_BASIC:
                    raise
                self.logger.warning("Falling back to basic scoring")
                engagement_scores = []
        
        # If enhanced scoring failed or is disabled, use parent class method
        if not engagement_scores:
            self.logger.info("Using basic highlights extraction")
            return super().extract(transcription_data, video_path, keywords, target_length, output_dir, params)
        
        # Convert engagement scores to highlights format
        highlights = self._convert_engagement_scores_to_highlights(engagement_scores, target_length)
        
        # Apply final filtering and optimization
        optimized_highlights = self._optimize_highlights_selection(highlights, target_length)
        
        # Generate I-frame data for optimized highlights
        iframe_data = self._generate_iframe_data(optimized_highlights, video_path, output_dir)
        
        # Calculate processing metrics
        processing_time = time.time() - start_time
        
        # Prepare enhanced results
        result = {
            'highlights': optimized_highlights,
            'iframe_data': iframe_data,
            'engagement_analysis': self._generate_engagement_analysis(engagement_scores),
            'processing_metrics': self._generate_processing_metrics(processing_time),
            'extraction_metadata': {
                'total_segments_processed': len(segments),
                'engagement_scores_generated': len(engagement_scores),
                'final_highlights_count': len(optimized_highlights),
                'total_duration': sum(h['duration'] for h in optimized_highlights),
                'target_duration': target_length,
                'keywords_used': keywords,
                'enhanced_scoring_used': True,
                'processing_time': processing_time
            }
        }
        
        self.logger.info(f"🎯 Enhanced extraction completed in {processing_time:.2f}s")
        self.logger.info(f"📈 Generated {len(optimized_highlights)} highlights with {sum(h['duration'] for h in optimized_highlights):.1f}s total duration")
        
        return result
    
    def _convert_engagement_scores_to_highlights(self, engagement_scores: List[EngagementScore], 
                                               target_length: int) -> List[Dict[str, Any]]:
        """Convert engagement scores to highlights format"""
        highlights = []
        
        # Filter high-engagement segments
        high_engagement = self.engagement_scorer.get_high_engagement_segments(engagement_scores)
        
        for score in high_engagement:
            highlight = {
                'start_time': score.start_time,
                'end_time': score.end_time,
                'duration': score.duration,
                'text': score.text,
                'score': score.confidence_score,
                'engagement_score': score.composite_score,
                'score_per_second': score.composite_score / score.duration if score.duration > 0 else 0.0,
                
                # Enhanced scoring details
                'emotion_score': score.emotion_score,
                'rhetorical_score': score.rhetorical_score,
                'topic_transition_score': score.topic_transition_score,
                'keyword_significance_score': score.keyword_significance_score,
                
                # Metadata
                'engagement_type': score.engagement_type,
                'segment_id': score.segment_id,
                'metadata': score.metadata
            }
            highlights.append(highlight)
        
        # Sort by engagement score descending
        highlights.sort(key=lambda x: x['engagement_score'], reverse=True)
        
        return highlights
    
    def _optimize_highlights_selection(self, highlights: List[Dict[str, Any]], 
                                     target_length: int) -> List[Dict[str, Any]]:
        """Optimize highlights selection to meet target duration"""
        if not highlights:
            return []
        
        # Greedy selection algorithm optimized for engagement
        selected_highlights = []
        total_duration = 0.0
        
        # Sort by score per second for optimal selection
        sorted_highlights = sorted(highlights, key=lambda x: x['score_per_second'], reverse=True)
        
        for highlight in sorted_highlights:
            if total_duration + highlight['duration'] <= target_length:
                selected_highlights.append(highlight)
                total_duration += highlight['duration']
            elif total_duration < target_length * 0.8:  # Allow some flexibility
                # Try to fit a shorter segment
                remaining_time = target_length - total_duration
                if highlight['duration'] <= remaining_time * 1.2:  # 20% tolerance
                    selected_highlights.append(highlight)
                    break
        
        # Sort selected highlights by start time for chronological order
        selected_highlights.sort(key=lambda x: x['start_time'])
        
        return selected_highlights
    
    def _generate_engagement_analysis(self, engagement_scores: List[EngagementScore]) -> Dict[str, Any]:
        """Generate comprehensive engagement analysis"""
        if not self.engagement_scorer or not engagement_scores:
            return {}
        
        return self.engagement_scorer.analyze_engagement_distribution(engagement_scores)
    
    def _generate_processing_metrics(self, processing_time: float) -> Dict[str, Any]:
        """Generate processing performance metrics"""
        metrics = {
            'total_processing_time': processing_time,
            'enhanced_scoring_enabled': self.engagement_scorer is not None
        }
        
        if self.engagement_scorer:
            scorer_metrics = self.engagement_scorer.get_performance_metrics()
            metrics.update(scorer_metrics)
        
        return metrics
    
    def get_engagement_scorer_metrics(self) -> Dict[str, Any]:
        """Get detailed metrics from the engagement scorer"""
        if not self.engagement_scorer:
            return {'error': 'Engagement scorer not available'}
        
        return self.engagement_scorer.get_performance_metrics()
    
    def clear_engagement_cache(self):
        """Clear the engagement scoring cache"""
        if self.engagement_scorer:
            self.engagement_scorer.clear_cache()
            self.logger.info("Engagement scoring cache cleared")
    
    def is_enhanced_scoring_available(self) -> bool:
        """Check if enhanced scoring is available"""
        return self.engagement_scorer is not None
