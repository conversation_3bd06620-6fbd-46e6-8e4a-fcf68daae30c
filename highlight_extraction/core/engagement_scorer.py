#!/usr/bin/env python3
"""
Sophisticated Engagement Scoring System

Advanced NLP-based engagement scoring using local ML models for emotion detection,
rhetorical analysis, topic transition detection, and keyword significance scoring.
"""

import os
import re
import time
import logging
import hashlib
import pickle
from typing import Dict, Any, List, Optional, Tuple, Union
from collections import defaultdict, Counter
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, as_completed
import threading

# Core dependencies
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

# ML model dependencies (with graceful fallbacks)
try:
    import torch
    import torch.nn.functional as F
    from transformers import AutoTokenizer, AutoModelForSequenceClassification, pipeline
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False

try:
    from bertopic import BERTopic
    from umap import UMAP
    from hdbscan import HDBSCAN
    BERTOPIC_AVAILABLE = True
except ImportError:
    BERTOPIC_AVAILABLE = False

# Local imports
from ..models.engagement_models import (
    EngagementScore, EngagementMetadata, ScoringConfig, 
    EngagementType, ProcessingMode
)
from ..config.engagement_settings import (
    ENGAGEMENT_SCORING_USE_GPU, ENGAGEMENT_SCORING_GPU_FALLBACK,
    ENGAGEMENT_SCORING_BATCH_SIZE, ENGAGEMENT_EMOTION_MODEL,
    ENGAGEMENT_SENTENCE_TRANSFORMER_MODEL, ENGAGEMENT_SPACY_MODEL,
    ENGAGEMENT_EMOTION_WEIGHT, ENGAGEMENT_RHETORICAL_WEIGHT,
    ENGAGEMENT_TOPIC_TRANSITION_WEIGHT, ENGAGEMENT_KEYWORD_SIGNIFICANCE_WEIGHT,
    ENGAGEMENT_RHETORICAL_PATTERNS, ENGAGEMENT_ENABLE_CACHING,
    ENGAGEMENT_CACHE_DIR, ENGAGEMENT_MIN_CONFIDENCE_THRESHOLD,
    ENGAGEMENT_MIN_SEGMENT_DURATION, ENGAGEMENT_MAX_SEGMENT_DURATION
)


class EngagementScorer:
    """
    Sophisticated engagement scoring system with GPU acceleration and CPU fallback.
    
    Implements advanced NLP features:
    - Emotion detection using GoEmotions/DistilRoBERTa
    - Rhetorical analysis using spaCy and pattern matching
    - Topic transition detection using BERTopic
    - Keyword significance using BERT embeddings and TF-IDF
    """
    
    def __init__(self, config: Optional[ScoringConfig] = None):
        """
        Initialize the engagement scoring system
        
        Args:
            config: Optional scoring configuration
        """
        self.config = config or ScoringConfig.from_env()
        self.logger = logging.getLogger(__name__)
        
        # Initialize processing state
        self.device = self._determine_device()
        self.processing_mode = self._determine_processing_mode()
        
        # Model storage
        self.emotion_model = None
        self.emotion_tokenizer = None
        self.sentence_transformer = None
        self.spacy_nlp = None
        self.topic_model = None
        
        # Caching system
        self.cache = {}
        self.cache_lock = threading.Lock()
        
        # Performance metrics
        self.metrics = {
            'total_segments_processed': 0,
            'gpu_processing_time': 0.0,
            'cpu_processing_time': 0.0,
            'cache_hits': 0,
            'cache_misses': 0,
            'model_load_time': 0.0
        }
        
        # Initialize models
        self._initialize_models()
        
        self.logger.info(f"EngagementScorer initialized with device: {self.device}, mode: {self.processing_mode}")
    
    def _determine_device(self) -> str:
        """Determine the best available device for processing"""
        if not self.config.use_gpu:
            return "cpu"
        
        if TRANSFORMERS_AVAILABLE and torch.cuda.is_available():
            return "cuda"
        elif TRANSFORMERS_AVAILABLE and hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            return "mps"
        else:
            if self.config.gpu_fallback_enabled:
                self.logger.warning("GPU requested but not available, falling back to CPU")
                return "cpu"
            else:
                raise RuntimeError("GPU requested but not available and fallback disabled")
    
    def _determine_processing_mode(self) -> ProcessingMode:
        """Determine the processing mode based on device and configuration"""
        if self.device != "cpu":
            return ProcessingMode.GPU_ACCELERATED
        else:
            return ProcessingMode.CPU_FALLBACK
    
    def _initialize_models(self):
        """Initialize all ML models with error handling and fallbacks"""
        start_time = time.time()
        
        try:
            self._initialize_emotion_model()
            self._initialize_sentence_transformer()
            self._initialize_spacy_model()
            self._initialize_topic_model()
            
            self.metrics['model_load_time'] = time.time() - start_time
            self.logger.info(f"All models initialized successfully in {self.metrics['model_load_time']:.2f}s")
            
        except Exception as e:
            self.logger.error(f"Error initializing models: {str(e)}")
            if not self.config.gpu_fallback_enabled:
                raise
            
            # Try CPU fallback
            self.logger.warning("Attempting CPU fallback for model initialization")
            self.device = "cpu"
            self.processing_mode = ProcessingMode.CPU_FALLBACK
            self._initialize_models()
    
    def _initialize_emotion_model(self):
        """Initialize emotion detection model"""
        if not TRANSFORMERS_AVAILABLE:
            self.logger.warning("Transformers not available, emotion detection will be limited")
            return
        
        try:
            self.logger.info(f"Loading emotion model: {self.config.emotion_model_name}")
            
            self.emotion_tokenizer = AutoTokenizer.from_pretrained(self.config.emotion_model_name)
            self.emotion_model = AutoModelForSequenceClassification.from_pretrained(
                self.config.emotion_model_name
            )
            
            if self.device != "cpu":
                self.emotion_model = self.emotion_model.to(self.device)
            
            self.emotion_model.eval()
            self.logger.info("Emotion model loaded successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to load emotion model: {str(e)}")
            self.emotion_model = None
            self.emotion_tokenizer = None
    
    def _initialize_sentence_transformer(self):
        """Initialize sentence transformer for semantic analysis"""
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            self.logger.warning("Sentence transformers not available, semantic analysis will be limited")
            return
        
        try:
            self.logger.info(f"Loading sentence transformer: {self.config.sentence_transformer_model}")
            
            self.sentence_transformer = SentenceTransformer(
                self.config.sentence_transformer_model,
                device=self.device
            )
            
            self.logger.info("Sentence transformer loaded successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to load sentence transformer: {str(e)}")
            self.sentence_transformer = None
    
    def _initialize_spacy_model(self):
        """Initialize spaCy model for rhetorical analysis"""
        if not SPACY_AVAILABLE:
            self.logger.warning("spaCy not available, rhetorical analysis will be limited")
            return
        
        try:
            self.logger.info(f"Loading spaCy model: {ENGAGEMENT_SPACY_MODEL}")
            
            # Try to load the model
            try:
                self.spacy_nlp = spacy.load(ENGAGEMENT_SPACY_MODEL)
            except OSError:
                # Model not found, try to download it
                self.logger.warning(f"spaCy model {ENGAGEMENT_SPACY_MODEL} not found, using basic English model")
                self.spacy_nlp = spacy.load("en_core_web_sm")
            
            # Disable unused components for performance
            disabled_components = ['ner', 'parser']
            for component in disabled_components:
                if component in self.spacy_nlp.pipe_names:
                    self.spacy_nlp.disable_pipe(component)
            
            self.logger.info("spaCy model loaded successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to load spaCy model: {str(e)}")
            self.spacy_nlp = None
    
    def _initialize_topic_model(self):
        """Initialize BERTopic model for topic transition detection"""
        if not BERTOPIC_AVAILABLE:
            self.logger.warning("BERTopic not available, topic transition detection will be limited")
            return
        
        try:
            self.logger.info("Initializing BERTopic model")
            
            # Configure UMAP and HDBSCAN for topic modeling
            umap_model = UMAP(
                n_neighbors=15,
                n_components=self.config.topic_model_n_components,
                min_dist=0.0,
                metric='cosine',
                random_state=42
            )
            
            hdbscan_model = HDBSCAN(
                min_cluster_size=self.config.topic_model_min_topic_size,
                metric='euclidean',
                cluster_selection_method='eom'
            )
            
            # Initialize BERTopic with custom components
            self.topic_model = BERTopic(
                embedding_model=self.sentence_transformer,
                umap_model=umap_model,
                hdbscan_model=hdbscan_model,
                calculate_probabilities=True,
                verbose=False
            )
            
            self.logger.info("BERTopic model initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize BERTopic model: {str(e)}")
            self.topic_model = None

    def score_segments(self, segments: List[Dict[str, Any]],
                      keywords: Optional[List[str]] = None) -> List[EngagementScore]:
        """
        Score multiple segments for engagement using batch processing

        Args:
            segments: List of transcript segments with 'text', 'start', 'end' fields
            keywords: Optional list of keywords for significance scoring

        Returns:
            List of EngagementScore objects
        """
        if not segments:
            return []

        start_time = time.time()
        self.logger.info(f"Scoring {len(segments)} segments for engagement")

        # Filter segments by duration and text length
        valid_segments = self._filter_segments(segments)
        self.logger.info(f"Filtered to {len(valid_segments)} valid segments")

        if not valid_segments:
            return []

        # Process in batches for optimal performance
        batch_size = min(self.config.batch_size, len(valid_segments))
        engagement_scores = []

        for i in range(0, len(valid_segments), batch_size):
            batch = valid_segments[i:i + batch_size]
            batch_scores = self._score_batch(batch, keywords)
            engagement_scores.extend(batch_scores)

        # Update metrics
        processing_time = time.time() - start_time
        self.metrics['total_segments_processed'] += len(valid_segments)

        if self.device != "cpu":
            self.metrics['gpu_processing_time'] += processing_time
        else:
            self.metrics['cpu_processing_time'] += processing_time

        self.logger.info(f"Scored {len(engagement_scores)} segments in {processing_time:.2f}s")
        return engagement_scores

    def _filter_segments(self, segments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter segments based on duration and text quality"""
        valid_segments = []

        for segment in segments:
            # Check duration
            start_time = segment.get('start', 0.0)
            end_time = segment.get('end', 0.0)
            duration = end_time - start_time

            if duration < self.config.min_segment_duration or duration > self.config.max_segment_duration:
                continue

            # Check text quality
            text = segment.get('text', '').strip()
            if len(text) < 10:  # Minimum text length
                continue

            valid_segments.append(segment)

        return valid_segments

    def _score_batch(self, batch: List[Dict[str, Any]],
                    keywords: Optional[List[str]] = None) -> List[EngagementScore]:
        """Score a batch of segments"""
        batch_texts = [segment.get('text', '') for segment in batch]

        # Check cache for batch results
        cache_keys = [self._get_cache_key(text, keywords) for text in batch_texts]
        cached_results = []
        uncached_indices = []

        if ENGAGEMENT_ENABLE_CACHING:
            with self.cache_lock:
                for i, cache_key in enumerate(cache_keys):
                    if cache_key in self.cache:
                        cached_results.append((i, self.cache[cache_key]))
                        self.metrics['cache_hits'] += 1
                    else:
                        uncached_indices.append(i)
                        self.metrics['cache_misses'] += 1
        else:
            uncached_indices = list(range(len(batch)))

        # Process uncached segments
        batch_scores = [None] * len(batch)

        # Set cached results
        for i, score in cached_results:
            batch_scores[i] = score

        if uncached_indices:
            # Extract uncached texts and segments
            uncached_texts = [batch_texts[i] for i in uncached_indices]
            uncached_segments = [batch[i] for i in uncached_indices]

            # Compute scores for uncached segments
            uncached_scores = self._compute_engagement_scores(uncached_segments, uncached_texts, keywords)

            # Store results
            for idx, score in zip(uncached_indices, uncached_scores):
                batch_scores[idx] = score

                # Cache the result
                if ENGAGEMENT_ENABLE_CACHING:
                    with self.cache_lock:
                        self.cache[cache_keys[idx]] = score

        return [score for score in batch_scores if score is not None]

    def _compute_engagement_scores(self, segments: List[Dict[str, Any]],
                                  texts: List[str],
                                  keywords: Optional[List[str]] = None) -> List[EngagementScore]:
        """Compute engagement scores for a batch of segments"""
        scores = []

        # Batch emotion detection
        emotion_scores = self._batch_emotion_detection(texts)

        # Batch semantic analysis for topic transitions
        embeddings = self._batch_semantic_analysis(texts)

        # Process each segment
        for i, (segment, text) in enumerate(zip(segments, texts)):
            try:
                # Individual component scores
                emotion_score = emotion_scores[i] if i < len(emotion_scores) else 0.0
                rhetorical_score = self._analyze_rhetorical_patterns(text)
                topic_transition_score = self._detect_topic_transition(text, embeddings, i)
                keyword_significance_score = self._calculate_keyword_significance(text, keywords)

                # Composite score using weighted combination
                composite_score = (
                    self.config.emotion_weight * emotion_score +
                    self.config.rhetorical_weight * rhetorical_score +
                    self.config.topic_transition_weight * topic_transition_score +
                    self.config.keyword_significance_weight * keyword_significance_score
                )

                # Create engagement score object
                engagement_score = EngagementScore(
                    start_time=segment.get('start', 0.0),
                    end_time=segment.get('end', 0.0),
                    engagement_type=EngagementType.COMPOSITE.value,
                    confidence_score=min(1.0, max(0.0, composite_score)),
                    emotion_score=emotion_score,
                    rhetorical_score=rhetorical_score,
                    topic_transition_score=topic_transition_score,
                    keyword_significance_score=keyword_significance_score,
                    composite_score=composite_score,
                    text=text,
                    segment_id=segment.get('id'),
                    metadata=self._create_metadata(segment, {
                        'emotion_score': emotion_score,
                        'rhetorical_score': rhetorical_score,
                        'topic_transition_score': topic_transition_score,
                        'keyword_significance_score': keyword_significance_score
                    })
                )

                scores.append(engagement_score)

            except Exception as e:
                self.logger.error(f"Error scoring segment {i}: {str(e)}")
                continue

        return scores

    def _batch_emotion_detection(self, texts: List[str]) -> List[float]:
        """Perform batch emotion detection using GoEmotions/DistilRoBERTa"""
        if not self.emotion_model or not self.emotion_tokenizer:
            self.logger.warning("Emotion model not available, returning default scores")
            return [0.5] * len(texts)

        try:
            # Tokenize batch
            inputs = self.emotion_tokenizer(
                texts,
                padding=True,
                truncation=True,
                max_length=512,
                return_tensors="pt"
            )

            if self.device != "cpu":
                inputs = {k: v.to(self.device) for k, v in inputs.items()}

            # Forward pass
            with torch.no_grad():
                outputs = self.emotion_model(**inputs)
                logits = outputs.logits

                # Apply softmax to get probabilities
                probabilities = F.softmax(logits, dim=-1)

                # Calculate engagement scores based on emotion intensity
                emotion_scores = []
                for prob in probabilities:
                    # Focus on high-engagement emotions (joy, surprise, anger, fear)
                    # Adjust indices based on your specific emotion model
                    engagement_emotions = [0, 1, 2, 3]  # Adjust based on model labels
                    engagement_score = sum(prob[i].item() for i in engagement_emotions if i < len(prob))
                    emotion_scores.append(min(1.0, engagement_score))

                return emotion_scores

        except Exception as e:
            self.logger.error(f"Error in batch emotion detection: {str(e)}")
            return [0.5] * len(texts)

    def _batch_semantic_analysis(self, texts: List[str]) -> Optional[np.ndarray]:
        """Perform batch semantic analysis for embeddings"""
        if not self.sentence_transformer:
            self.logger.warning("Sentence transformer not available")
            return None

        try:
            embeddings = self.sentence_transformer.encode(
                texts,
                batch_size=self.config.batch_size,
                show_progress_bar=False,
                convert_to_numpy=True
            )
            return embeddings

        except Exception as e:
            self.logger.error(f"Error in batch semantic analysis: {str(e)}")
            return None

    def _analyze_rhetorical_patterns(self, text: str) -> float:
        """Analyze rhetorical patterns in text"""
        if not text.strip():
            return 0.0

        rhetorical_score = 0.0
        pattern_count = 0

        # Question detection
        question_patterns = ENGAGEMENT_RHETORICAL_PATTERNS['questions']
        for pattern in question_patterns:
            matches = len(re.findall(pattern, text, re.IGNORECASE))
            if matches > 0:
                rhetorical_score += 0.3 * min(1.0, matches / 3.0)  # Normalize by expected frequency
                pattern_count += matches

        # Emphasis detection
        emphasis_patterns = ENGAGEMENT_RHETORICAL_PATTERNS['emphasis']
        for pattern in emphasis_patterns:
            matches = len(re.findall(pattern, text, re.IGNORECASE))
            if matches > 0:
                rhetorical_score += 0.2 * min(1.0, matches / 2.0)
                pattern_count += matches

        # Repetition detection
        repetition_patterns = ENGAGEMENT_RHETORICAL_PATTERNS['repetition']
        for pattern in repetition_patterns:
            matches = len(re.findall(pattern, text, re.IGNORECASE))
            if matches > 0:
                rhetorical_score += 0.25 * min(1.0, matches / 2.0)
                pattern_count += matches

        # spaCy-based analysis (if available)
        if self.spacy_nlp:
            try:
                doc = self.spacy_nlp(text)

                # Analyze sentence structure for engagement markers
                for sent in doc.sents:
                    # Check for imperative mood (commands)
                    if any(token.tag_ in ['VB', 'VBP'] and token.dep_ == 'ROOT' for token in sent):
                        rhetorical_score += 0.1

                    # Check for exclamatory sentences
                    if sent.text.strip().endswith('!'):
                        rhetorical_score += 0.15

            except Exception as e:
                self.logger.debug(f"spaCy analysis failed: {str(e)}")

        return min(1.0, rhetorical_score)

    def _detect_topic_transition(self, text: str, embeddings: Optional[np.ndarray],
                               current_index: int) -> float:
        """Detect topic transitions using semantic similarity"""
        if embeddings is None or current_index == 0:
            return 0.0

        try:
            current_embedding = embeddings[current_index].reshape(1, -1)

            # Compare with previous segments (sliding window)
            window_size = min(5, current_index)
            similarities = []

            for i in range(max(0, current_index - window_size), current_index):
                prev_embedding = embeddings[i].reshape(1, -1)
                similarity = cosine_similarity(current_embedding, prev_embedding)[0][0]
                similarities.append(similarity)

            if similarities:
                avg_similarity = np.mean(similarities)
                # Lower similarity indicates topic transition
                transition_score = 1.0 - avg_similarity
                return max(0.0, min(1.0, transition_score))

        except Exception as e:
            self.logger.debug(f"Topic transition detection failed: {str(e)}")

        return 0.0

    def _calculate_keyword_significance(self, text: str,
                                      keywords: Optional[List[str]] = None) -> float:
        """Calculate keyword significance using TF-IDF and semantic similarity"""
        if not keywords or not text.strip():
            return 0.0

        significance_score = 0.0

        # Simple keyword matching
        text_lower = text.lower()
        keyword_matches = sum(1 for keyword in keywords if keyword.lower() in text_lower)
        if keyword_matches > 0:
            significance_score += 0.3 * min(1.0, keyword_matches / len(keywords))

        # Semantic similarity with keywords (if sentence transformer available)
        if self.sentence_transformer and keywords:
            try:
                text_embedding = self.sentence_transformer.encode([text])
                keyword_embeddings = self.sentence_transformer.encode(keywords)

                # Calculate maximum similarity with any keyword
                similarities = cosine_similarity(text_embedding, keyword_embeddings)[0]
                max_similarity = np.max(similarities) if len(similarities) > 0 else 0.0

                significance_score += 0.7 * max_similarity

            except Exception as e:
                self.logger.debug(f"Semantic keyword analysis failed: {str(e)}")

        return min(1.0, significance_score)

    def _get_cache_key(self, text: str, keywords: Optional[List[str]] = None) -> str:
        """Generate cache key for text and keywords"""
        key_data = text
        if keywords:
            key_data += "|" + "|".join(sorted(keywords))

        return hashlib.md5(key_data.encode('utf-8')).hexdigest()

    def _create_metadata(self, segment: Dict[str, Any], scores: Dict[str, float]) -> Dict[str, Any]:
        """Create metadata for engagement score"""
        return {
            'processing_mode': self.processing_mode.value,
            'device_used': self.device,
            'model_versions': {
                'emotion_model': self.config.emotion_model_name,
                'sentence_transformer': self.config.sentence_transformer_model
            },
            'component_scores': scores,
            'segment_metadata': {
                'duration': segment.get('end', 0.0) - segment.get('start', 0.0),
                'avg_logprob': segment.get('avg_logprob'),
                'no_speech_prob': segment.get('no_speech_prob')
            }
        }

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for the scorer"""
        total_processing_time = self.metrics['gpu_processing_time'] + self.metrics['cpu_processing_time']
        cache_hit_rate = 0.0

        total_cache_requests = self.metrics['cache_hits'] + self.metrics['cache_misses']
        if total_cache_requests > 0:
            cache_hit_rate = self.metrics['cache_hits'] / total_cache_requests

        return {
            'total_segments_processed': self.metrics['total_segments_processed'],
            'total_processing_time': total_processing_time,
            'gpu_processing_time': self.metrics['gpu_processing_time'],
            'cpu_processing_time': self.metrics['cpu_processing_time'],
            'model_load_time': self.metrics['model_load_time'],
            'cache_hit_rate': cache_hit_rate,
            'cache_hits': self.metrics['cache_hits'],
            'cache_misses': self.metrics['cache_misses'],
            'device_used': self.device,
            'processing_mode': self.processing_mode.value,
            'models_loaded': {
                'emotion_model': self.emotion_model is not None,
                'sentence_transformer': self.sentence_transformer is not None,
                'spacy_model': self.spacy_nlp is not None,
                'topic_model': self.topic_model is not None
            }
        }

    def clear_cache(self):
        """Clear the scoring cache"""
        with self.cache_lock:
            self.cache.clear()
            self.logger.info("Engagement scoring cache cleared")

    def get_high_engagement_segments(self, engagement_scores: List[EngagementScore],
                                   threshold: float = None) -> List[EngagementScore]:
        """
        Filter engagement scores to return only high-engagement segments

        Args:
            engagement_scores: List of engagement scores
            threshold: Minimum confidence threshold (uses config default if None)

        Returns:
            List of high-engagement segments
        """
        if threshold is None:
            threshold = self.config.min_confidence_threshold

        high_engagement = [
            score for score in engagement_scores
            if score.confidence_score >= threshold
        ]

        # Sort by composite score descending
        high_engagement.sort(key=lambda x: x.composite_score, reverse=True)

        return high_engagement

    def analyze_engagement_distribution(self, engagement_scores: List[EngagementScore]) -> Dict[str, Any]:
        """
        Analyze the distribution of engagement scores

        Args:
            engagement_scores: List of engagement scores

        Returns:
            Statistical analysis of engagement distribution
        """
        if not engagement_scores:
            return {}

        # Extract scores
        composite_scores = [score.composite_score for score in engagement_scores]
        emotion_scores = [score.emotion_score for score in engagement_scores]
        rhetorical_scores = [score.rhetorical_score for score in engagement_scores]
        topic_scores = [score.topic_transition_score for score in engagement_scores]
        keyword_scores = [score.keyword_significance_score for score in engagement_scores]

        def calculate_stats(scores: List[float]) -> Dict[str, float]:
            if not scores:
                return {}
            return {
                'mean': np.mean(scores),
                'median': np.median(scores),
                'std': np.std(scores),
                'min': np.min(scores),
                'max': np.max(scores),
                'q25': np.percentile(scores, 25),
                'q75': np.percentile(scores, 75)
            }

        return {
            'total_segments': len(engagement_scores),
            'high_engagement_count': len([s for s in engagement_scores if s.confidence_score >= self.config.min_confidence_threshold]),
            'composite_scores': calculate_stats(composite_scores),
            'emotion_scores': calculate_stats(emotion_scores),
            'rhetorical_scores': calculate_stats(rhetorical_scores),
            'topic_transition_scores': calculate_stats(topic_scores),
            'keyword_significance_scores': calculate_stats(keyword_scores),
            'score_distribution': {
                'low_engagement': len([s for s in composite_scores if s < 0.3]),
                'medium_engagement': len([s for s in composite_scores if 0.3 <= s < 0.7]),
                'high_engagement': len([s for s in composite_scores if s >= 0.7])
            }
        }
