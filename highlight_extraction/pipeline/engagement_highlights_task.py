#!/usr/bin/env python3
"""
Engagement Highlights Pipeline Task

Pipeline task wrapper for the enhanced engagement scoring system.
Integrates seamlessly with the existing video processing pipeline.
"""

import os
import time
import json
import logging
from typing import Dict, Any, Optional

from pipeline.tasks.base_task import BaseTask
from ..core.enhanced_intelligent_extractor import EnhancedIntelligentExtractor
from ..models.engagement_models import ScoringConfig
from ..config.engagement_settings import (
    ENGAGEMENT_SCORING_ENABLED, ENGAGEMENT_SCORING_FALLBACK_TO_BASIC,
    get_engagement_config_summary
)


class EngagementHighlightsTask(BaseTask):
    """
    Pipeline task for sophisticated engagement-based highlights extraction.
    
    This task runs after the transcription stage and provides advanced
    engagement scoring using local ML models for emotion detection,
    rhetorical analysis, topic transition detection, and keyword significance.
    """
    
    task_name = "engagement_highlights"
    requires_gpu = True  # Benefits from GPU acceleration
    
    def __init__(self, config: Optional[ScoringConfig] = None):
        """
        Initialize the engagement highlights task
        
        Args:
            config: Optional engagement scoring configuration
        """
        super().__init__()
        self.config = config or ScoringConfig.from_env()
        self.extractor = None
        
        # Initialize extractor if engagement scoring is enabled
        if ENGAGEMENT_SCORING_ENABLED:
            try:
                self.extractor = EnhancedIntelligentExtractor(self.config)
                self.logger.info("✨ Engagement highlights task initialized successfully")
            except Exception as e:
                self.logger.error(f"Failed to initialize engagement extractor: {str(e)}")
                if not ENGAGEMENT_SCORING_FALLBACK_TO_BASIC:
                    raise
                self.logger.warning("Engagement scoring disabled, task will skip processing")
                self.extractor = None
        else:
            self.logger.info("Engagement scoring disabled in configuration")
    
    def run(self, job_id: str, transcription_result: Dict[str, Any],
            video_ingestor_result: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Run engagement-based highlights extraction
        
        Args:
            job_id: Unique identifier for the job
            transcription_result: Results from transcription engine
            video_ingestor_result: Results from video ingestor
            params: Additional parameters including keywords and target length
            
        Returns:
            Task result with engagement highlights and analysis
        """
        start_time = time.time()
        self.logger.info(f"🚀 Starting engagement highlights extraction for job {job_id}")
        
        # Check if engagement scoring is available
        if not self.extractor:
            self.logger.warning("Engagement extractor not available, skipping task")
            return {
                'status': 'skipped',
                'reason': 'engagement_scoring_disabled',
                'job_id': job_id,
                'processing_time': time.time() - start_time,
                'config_summary': get_engagement_config_summary()
            }
        
        try:
            # Extract required data from previous stages
            transcript_path = transcription_result.get('transcript_path')
            if not transcript_path or not os.path.exists(transcript_path):
                raise ValueError(f"Transcript file not found: {transcript_path}")
            
            # Load transcription data
            with open(transcript_path, 'r') as f:
                transcription_data = json.load(f)
            
            # Get video path
            video_path = video_ingestor_result.get('optimized_path') or video_ingestor_result.get('video_path')
            if not video_path or not os.path.exists(video_path):
                raise ValueError(f"Video file not found: {video_path}")
            
            # Extract parameters
            keywords = params.get('keywords', [])
            target_length = params.get('target_length', 75)
            
            # Create output directory
            output_dir = os.path.join(transcription_result.get('output_dir', 'output'), 'engagement_highlights')
            os.makedirs(output_dir, exist_ok=True)
            
            self.logger.info(f"📊 Processing {len(transcription_data.get('segments', []))} segments")
            self.logger.info(f"🎯 Target duration: {target_length}s, Keywords: {len(keywords)}")
            
            # Run enhanced extraction
            extraction_result = self.extractor.extract(
                transcription_data=transcription_data,
                video_path=video_path,
                keywords=keywords,
                target_length=target_length,
                output_dir=output_dir,
                params=params
            )
            
            # Save engagement analysis
            engagement_analysis_path = os.path.join(output_dir, 'engagement_analysis.json')
            with open(engagement_analysis_path, 'w') as f:
                json.dump(extraction_result.get('engagement_analysis', {}), f, indent=2)
            
            # Save processing metrics
            metrics_path = os.path.join(output_dir, 'processing_metrics.json')
            with open(metrics_path, 'w') as f:
                json.dump(extraction_result.get('processing_metrics', {}), f, indent=2)
            
            # Save highlights with engagement scores
            highlights_path = os.path.join(output_dir, 'engagement_highlights.json')
            with open(highlights_path, 'w') as f:
                json.dump(extraction_result.get('highlights', []), f, indent=2)
            
            # Calculate final metrics
            processing_time = time.time() - start_time
            highlights = extraction_result.get('highlights', [])
            total_duration = sum(h.get('duration', 0) for h in highlights)
            
            self.logger.info(f"✅ Engagement extraction completed in {processing_time:.2f}s")
            self.logger.info(f"📈 Generated {len(highlights)} highlights with {total_duration:.1f}s total duration")
            
            # Prepare task result
            result = {
                'status': 'completed',
                'job_id': job_id,
                'highlights_count': len(highlights),
                'total_duration': total_duration,
                'target_duration': target_length,
                'processing_time': processing_time,
                
                # File paths
                'engagement_analysis_path': engagement_analysis_path,
                'processing_metrics_path': metrics_path,
                'highlights_path': highlights_path,
                'output_dir': output_dir,
                
                # Summary data
                'highlights': highlights,
                'engagement_analysis': extraction_result.get('engagement_analysis', {}),
                'processing_metrics': extraction_result.get('processing_metrics', {}),
                'extraction_metadata': extraction_result.get('extraction_metadata', {}),
                
                # Configuration
                'config_summary': get_engagement_config_summary(),
                'enhanced_scoring_used': self.extractor.is_enhanced_scoring_available()
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in engagement highlights extraction: {str(e)}")
            
            # Return error result
            return {
                'status': 'error',
                'error': str(e),
                'job_id': job_id,
                'processing_time': time.time() - start_time,
                'config_summary': get_engagement_config_summary()
            }
    
    def get_task_info(self) -> Dict[str, Any]:
        """Get information about the task configuration"""
        return {
            'task_name': self.task_name,
            'requires_gpu': self.requires_gpu,
            'engagement_scoring_enabled': ENGAGEMENT_SCORING_ENABLED,
            'extractor_available': self.extractor is not None,
            'enhanced_scoring_available': self.extractor.is_enhanced_scoring_available() if self.extractor else False,
            'config_summary': get_engagement_config_summary(),
            'scorer_metrics': self.extractor.get_engagement_scorer_metrics() if self.extractor else {}
        }
    
    def clear_cache(self):
        """Clear engagement scoring cache"""
        if self.extractor:
            self.extractor.clear_engagement_cache()
            self.logger.info("Engagement scoring cache cleared")
    
    def validate_dependencies(self) -> Dict[str, bool]:
        """Validate that all required dependencies are available"""
        dependencies = {
            'transformers': False,
            'sentence_transformers': False,
            'spacy': False,
            'bertopic': False,
            'torch': False,
            'sklearn': False,
            'numpy': False
        }
        
        try:
            import transformers
            dependencies['transformers'] = True
        except ImportError:
            pass
        
        try:
            import sentence_transformers
            dependencies['sentence_transformers'] = True
        except ImportError:
            pass
        
        try:
            import spacy
            dependencies['spacy'] = True
        except ImportError:
            pass
        
        try:
            import bertopic
            dependencies['bertopic'] = True
        except ImportError:
            pass
        
        try:
            import torch
            dependencies['torch'] = True
        except ImportError:
            pass
        
        try:
            import sklearn
            dependencies['sklearn'] = True
        except ImportError:
            pass
        
        try:
            import numpy
            dependencies['numpy'] = True
        except ImportError:
            pass
        
        return dependencies


# Create task instance for direct usage
engagement_highlights_task = EngagementHighlightsTask()


def extract_engagement_highlights(job_id: str, transcription_result: Dict[str, Any],
                                video_ingestor_result: Dict[str, Any], 
                                params: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Direct function interface for engagement highlights extraction
    
    Args:
        job_id: Unique identifier for the job
        transcription_result: Results from transcription engine
        video_ingestor_result: Results from video ingestor
        params: Additional parameters
        
    Returns:
        Task result with engagement highlights
    """
    return engagement_highlights_task.run(job_id, transcription_result, video_ingestor_result, params or {})
