#!/usr/bin/env python3
"""
Engagement Scoring Data Models

Comprehensive data models for the sophisticated engagement scoring system.
"""

import os
from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional, Union
from enum import Enum


class EngagementType(Enum):
    """Types of engagement detected in content"""
    EMOTION = "emotion"
    RHETORICAL = "rhetorical" 
    TOPIC_TRANSITION = "topic_transition"
    KEYWORD_SIGNIFICANCE = "keyword_significance"
    COMPOSITE = "composite"


class ProcessingMode(Enum):
    """Processing modes for engagement scoring"""
    GPU_ACCELERATED = "gpu_accelerated"
    CPU_FALLBACK = "cpu_fallback"
    BATCH_PROCESSING = "batch_processing"
    SINGLE_SEGMENT = "single_segment"


@dataclass
class EngagementScore:
    """
    Standardized engagement score data structure
    
    Represents a scored segment with comprehensive engagement metrics
    following the required JSON schema standards.
    """
    start_time: float
    end_time: float
    engagement_type: str
    confidence_score: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # Additional scoring details
    emotion_score: float = 0.0
    rhetorical_score: float = 0.0
    topic_transition_score: float = 0.0
    keyword_significance_score: float = 0.0
    composite_score: float = 0.0
    
    # Text content and context
    text: str = ""
    segment_id: Optional[int] = None
    
    def __post_init__(self):
        """Validate and normalize score values"""
        # Ensure confidence_score is within valid range
        self.confidence_score = max(0.0, min(1.0, self.confidence_score))
        
        # Normalize individual scores
        self.emotion_score = max(0.0, min(1.0, self.emotion_score))
        self.rhetorical_score = max(0.0, min(1.0, self.rhetorical_score))
        self.topic_transition_score = max(0.0, min(1.0, self.topic_transition_score))
        self.keyword_significance_score = max(0.0, min(1.0, self.keyword_significance_score))
        self.composite_score = max(0.0, min(1.0, self.composite_score))
        
        # Ensure engagement_type is valid
        if isinstance(self.engagement_type, EngagementType):
            self.engagement_type = self.engagement_type.value
    
    @property
    def duration(self) -> float:
        """Calculate segment duration"""
        return max(0.0, self.end_time - self.start_time)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'start_time': self.start_time,
            'end_time': self.end_time,
            'engagement_type': self.engagement_type,
            'confidence_score': self.confidence_score,
            'metadata': self.metadata,
            'emotion_score': self.emotion_score,
            'rhetorical_score': self.rhetorical_score,
            'topic_transition_score': self.topic_transition_score,
            'keyword_significance_score': self.keyword_significance_score,
            'composite_score': self.composite_score,
            'text': self.text,
            'segment_id': self.segment_id,
            'duration': self.duration
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EngagementScore':
        """Create from dictionary"""
        # Extract required fields
        required_fields = {
            'start_time': data['start_time'],
            'end_time': data['end_time'],
            'engagement_type': data['engagement_type'],
            'confidence_score': data['confidence_score']
        }
        
        # Extract optional fields
        optional_fields = {
            'metadata': data.get('metadata', {}),
            'emotion_score': data.get('emotion_score', 0.0),
            'rhetorical_score': data.get('rhetorical_score', 0.0),
            'topic_transition_score': data.get('topic_transition_score', 0.0),
            'keyword_significance_score': data.get('keyword_significance_score', 0.0),
            'composite_score': data.get('composite_score', 0.0),
            'text': data.get('text', ''),
            'segment_id': data.get('segment_id')
        }
        
        return cls(**required_fields, **optional_fields)


@dataclass
class EngagementMetadata:
    """
    Additional metadata for engagement scoring context
    """
    processing_mode: ProcessingMode
    model_versions: Dict[str, str] = field(default_factory=dict)
    processing_time: float = 0.0
    gpu_used: bool = False
    cache_hit: bool = False
    batch_size: int = 1
    
    # Model-specific metadata
    emotion_model_confidence: float = 0.0
    topic_model_coherence: float = 0.0
    rhetorical_pattern_count: int = 0
    keyword_matches: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'processing_mode': self.processing_mode.value if isinstance(self.processing_mode, ProcessingMode) else self.processing_mode,
            'model_versions': self.model_versions,
            'processing_time': self.processing_time,
            'gpu_used': self.gpu_used,
            'cache_hit': self.cache_hit,
            'batch_size': self.batch_size,
            'emotion_model_confidence': self.emotion_model_confidence,
            'topic_model_coherence': self.topic_model_coherence,
            'rhetorical_pattern_count': self.rhetorical_pattern_count,
            'keyword_matches': self.keyword_matches
        }


@dataclass
class ScoringConfig:
    """
    Configuration for engagement scoring system
    """
    # GPU/CPU settings
    use_gpu: bool = True
    gpu_fallback_enabled: bool = True
    batch_size: int = 32
    max_batch_size: int = 128
    
    # Model settings
    emotion_model_name: str = "j-hartmann/emotion-english-distilroberta-base"
    topic_model_min_topic_size: int = 10
    topic_model_n_components: int = 5
    sentence_transformer_model: str = "all-MiniLM-L6-v2"
    
    # Scoring weights
    emotion_weight: float = 0.3
    rhetorical_weight: float = 0.25
    topic_transition_weight: float = 0.25
    keyword_significance_weight: float = 0.2
    
    # Quality thresholds
    min_confidence_threshold: float = 0.3
    min_segment_duration: float = 2.0
    max_segment_duration: float = 30.0
    
    # Caching settings
    enable_caching: bool = True
    cache_ttl_hours: int = 24
    cache_max_size: int = 1000
    
    # Performance settings
    processing_timeout: float = 300.0  # 5 minutes
    enable_parallel_processing: bool = True
    max_workers: int = 4
    
    @classmethod
    def from_env(cls) -> 'ScoringConfig':
        """Create configuration from environment variables"""
        return cls(
            use_gpu=os.getenv('ENGAGEMENT_SCORING_USE_GPU', 'true').lower() == 'true',
            gpu_fallback_enabled=os.getenv('ENGAGEMENT_SCORING_GPU_FALLBACK', 'true').lower() == 'true',
            batch_size=int(os.getenv('ENGAGEMENT_SCORING_BATCH_SIZE', '32')),
            max_batch_size=int(os.getenv('ENGAGEMENT_SCORING_MAX_BATCH_SIZE', '128')),
            
            emotion_model_name=os.getenv('ENGAGEMENT_EMOTION_MODEL', 'j-hartmann/emotion-english-distilroberta-base'),
            topic_model_min_topic_size=int(os.getenv('ENGAGEMENT_TOPIC_MIN_SIZE', '10')),
            topic_model_n_components=int(os.getenv('ENGAGEMENT_TOPIC_COMPONENTS', '5')),
            sentence_transformer_model=os.getenv('ENGAGEMENT_SENTENCE_MODEL', 'all-MiniLM-L6-v2'),
            
            emotion_weight=float(os.getenv('ENGAGEMENT_EMOTION_WEIGHT', '0.3')),
            rhetorical_weight=float(os.getenv('ENGAGEMENT_RHETORICAL_WEIGHT', '0.25')),
            topic_transition_weight=float(os.getenv('ENGAGEMENT_TOPIC_WEIGHT', '0.25')),
            keyword_significance_weight=float(os.getenv('ENGAGEMENT_KEYWORD_WEIGHT', '0.2')),
            
            min_confidence_threshold=float(os.getenv('ENGAGEMENT_MIN_CONFIDENCE', '0.3')),
            min_segment_duration=float(os.getenv('ENGAGEMENT_MIN_DURATION', '2.0')),
            max_segment_duration=float(os.getenv('ENGAGEMENT_MAX_DURATION', '30.0')),
            
            enable_caching=os.getenv('ENGAGEMENT_ENABLE_CACHING', 'true').lower() == 'true',
            cache_ttl_hours=int(os.getenv('ENGAGEMENT_CACHE_TTL_HOURS', '24')),
            cache_max_size=int(os.getenv('ENGAGEMENT_CACHE_MAX_SIZE', '1000')),
            
            processing_timeout=float(os.getenv('ENGAGEMENT_PROCESSING_TIMEOUT', '300.0')),
            enable_parallel_processing=os.getenv('ENGAGEMENT_PARALLEL_PROCESSING', 'true').lower() == 'true',
            max_workers=int(os.getenv('ENGAGEMENT_MAX_WORKERS', '4'))
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'use_gpu': self.use_gpu,
            'gpu_fallback_enabled': self.gpu_fallback_enabled,
            'batch_size': self.batch_size,
            'max_batch_size': self.max_batch_size,
            'emotion_model_name': self.emotion_model_name,
            'topic_model_min_topic_size': self.topic_model_min_topic_size,
            'topic_model_n_components': self.topic_model_n_components,
            'sentence_transformer_model': self.sentence_transformer_model,
            'emotion_weight': self.emotion_weight,
            'rhetorical_weight': self.rhetorical_weight,
            'topic_transition_weight': self.topic_transition_weight,
            'keyword_significance_weight': self.keyword_significance_weight,
            'min_confidence_threshold': self.min_confidence_threshold,
            'min_segment_duration': self.min_segment_duration,
            'max_segment_duration': self.max_segment_duration,
            'enable_caching': self.enable_caching,
            'cache_ttl_hours': self.cache_ttl_hours,
            'cache_max_size': self.cache_max_size,
            'processing_timeout': self.processing_timeout,
            'enable_parallel_processing': self.enable_parallel_processing,
            'max_workers': self.max_workers
        }
