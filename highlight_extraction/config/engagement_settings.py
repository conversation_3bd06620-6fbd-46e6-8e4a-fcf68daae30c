#!/usr/bin/env python3
"""
Engagement Scoring Configuration Settings

Environment-based configuration for the sophisticated engagement scoring system.
"""

import os
from typing import Dict, Any, List


# GPU and Performance Settings
ENGAGEMENT_SCORING_USE_GPU = os.getenv('ENGAGEMENT_SCORING_USE_GPU', 'true').lower() == 'true'
ENGAGEMENT_SCORING_GPU_FALLBACK = os.getenv('ENGAGEMENT_SCORING_GPU_FALLBACK', 'true').lower() == 'true'
ENGAGEMENT_SCORING_BATCH_SIZE = int(os.getenv('ENGAGEMENT_SCORING_BATCH_SIZE', '32'))
ENGAGEMENT_SCORING_MAX_BATCH_SIZE = int(os.getenv('ENGAGEMENT_SCORING_MAX_BATCH_SIZE', '128'))

# Model Configuration
ENGAGEMENT_EMOTION_MODEL = os.getenv('ENGAGEMENT_EMOTION_MODEL', 'j-hartmann/emotion-english-distilroberta-base')
ENGAGEMENT_TOPIC_MODEL_MIN_SIZE = int(os.getenv('ENGAGEMENT_TOPIC_MIN_SIZE', '10'))
ENGAGEMENT_TOPIC_MODEL_COMPONENTS = int(os.getenv('ENGAGEMENT_TOPIC_COMPONENTS', '5'))
ENGAGEMENT_SENTENCE_TRANSFORMER_MODEL = os.getenv('ENGAGEMENT_SENTENCE_MODEL', 'all-MiniLM-L6-v2')

# Alternative emotion models for fallback
ENGAGEMENT_EMOTION_MODEL_ALTERNATIVES = [
    'j-hartmann/emotion-english-distilroberta-base',
    'cardiffnlp/twitter-roberta-base-emotion',
    'SamLowe/roberta-base-go_emotions'
]

# Scoring Weights (must sum to 1.0)
ENGAGEMENT_EMOTION_WEIGHT = float(os.getenv('ENGAGEMENT_EMOTION_WEIGHT', '0.3'))
ENGAGEMENT_RHETORICAL_WEIGHT = float(os.getenv('ENGAGEMENT_RHETORICAL_WEIGHT', '0.25'))
ENGAGEMENT_TOPIC_TRANSITION_WEIGHT = float(os.getenv('ENGAGEMENT_TOPIC_WEIGHT', '0.25'))
ENGAGEMENT_KEYWORD_SIGNIFICANCE_WEIGHT = float(os.getenv('ENGAGEMENT_KEYWORD_WEIGHT', '0.2'))

# Quality and Filtering Thresholds
ENGAGEMENT_MIN_CONFIDENCE_THRESHOLD = float(os.getenv('ENGAGEMENT_MIN_CONFIDENCE', '0.3'))
ENGAGEMENT_MIN_SEGMENT_DURATION = float(os.getenv('ENGAGEMENT_MIN_DURATION', '2.0'))
ENGAGEMENT_MAX_SEGMENT_DURATION = float(os.getenv('ENGAGEMENT_MAX_DURATION', '30.0'))
ENGAGEMENT_MIN_TEXT_LENGTH = int(os.getenv('ENGAGEMENT_MIN_TEXT_LENGTH', '10'))

# Caching Configuration
ENGAGEMENT_ENABLE_CACHING = os.getenv('ENGAGEMENT_ENABLE_CACHING', 'true').lower() == 'true'
ENGAGEMENT_CACHE_TTL_HOURS = int(os.getenv('ENGAGEMENT_CACHE_TTL_HOURS', '24'))
ENGAGEMENT_CACHE_MAX_SIZE = int(os.getenv('ENGAGEMENT_CACHE_MAX_SIZE', '1000'))
ENGAGEMENT_CACHE_DIR = os.getenv('ENGAGEMENT_CACHE_DIR', 'cache/engagement_scoring')

# Processing Configuration
ENGAGEMENT_PROCESSING_TIMEOUT = float(os.getenv('ENGAGEMENT_PROCESSING_TIMEOUT', '300.0'))
ENGAGEMENT_ENABLE_PARALLEL_PROCESSING = os.getenv('ENGAGEMENT_PARALLEL_PROCESSING', 'true').lower() == 'true'
ENGAGEMENT_MAX_WORKERS = int(os.getenv('ENGAGEMENT_MAX_WORKERS', '4'))

# Emotion Detection Settings
ENGAGEMENT_EMOTION_THRESHOLD = float(os.getenv('ENGAGEMENT_EMOTION_THRESHOLD', '0.5'))
ENGAGEMENT_EMOTION_TOP_K = int(os.getenv('ENGAGEMENT_EMOTION_TOP_K', '3'))

# Rhetorical Analysis Settings
ENGAGEMENT_RHETORICAL_PATTERNS = {
    'questions': [
        r'\?',  # Question marks
        r'\b(what|how|why|when|where|who|which|whose)\b',  # Question words
        r'\b(can|could|would|should|will|shall|may|might|do|does|did|is|are|was|were)\b.*\?'  # Modal questions
    ],
    'emphasis': [
        r'[A-Z]{2,}',  # ALL CAPS
        r'\b(very|extremely|absolutely|completely|totally|really|truly|definitely)\b',  # Intensifiers
        r'!{1,}',  # Exclamation marks
        r'\b(amazing|incredible|fantastic|wonderful|terrible|awful|brilliant)\b'  # Strong adjectives
    ],
    'repetition': [
        r'\b(\w+)\s+\1\b',  # Word repetition
        r'\b(\w+)\s+(\w+)\s+\1\s+\2\b'  # Phrase repetition
    ]
}

# Topic Transition Detection Settings
ENGAGEMENT_TOPIC_SIMILARITY_THRESHOLD = float(os.getenv('ENGAGEMENT_TOPIC_SIMILARITY_THRESHOLD', '0.7'))
ENGAGEMENT_TOPIC_WINDOW_SIZE = int(os.getenv('ENGAGEMENT_TOPIC_WINDOW_SIZE', '5'))
ENGAGEMENT_TOPIC_MIN_TRANSITION_SCORE = float(os.getenv('ENGAGEMENT_TOPIC_MIN_TRANSITION_SCORE', '0.4'))

# Keyword Significance Settings
ENGAGEMENT_KEYWORD_CONTEXT_WINDOW = int(os.getenv('ENGAGEMENT_KEYWORD_CONTEXT_WINDOW', '3'))
ENGAGEMENT_KEYWORD_TF_IDF_THRESHOLD = float(os.getenv('ENGAGEMENT_KEYWORD_TF_IDF_THRESHOLD', '0.1'))
ENGAGEMENT_KEYWORD_SEMANTIC_THRESHOLD = float(os.getenv('ENGAGEMENT_KEYWORD_SEMANTIC_THRESHOLD', '0.6'))

# Logging Configuration
ENGAGEMENT_SCORING_LOG_LEVEL = os.getenv('ENGAGEMENT_SCORING_LOG_LEVEL', 'INFO')
ENGAGEMENT_SCORING_ENABLE_METRICS = os.getenv('ENGAGEMENT_SCORING_ENABLE_METRICS', 'true').lower() == 'true'

# Integration Settings
ENGAGEMENT_SCORING_ENABLED = os.getenv('ENGAGEMENT_SCORING_ENABLED', 'true').lower() == 'true'
ENGAGEMENT_SCORING_FALLBACK_TO_BASIC = os.getenv('ENGAGEMENT_SCORING_FALLBACK_TO_BASIC', 'true').lower() == 'true'

# Model Download and Storage Settings
ENGAGEMENT_MODELS_CACHE_DIR = os.getenv('ENGAGEMENT_MODELS_CACHE_DIR', 'models/engagement')
ENGAGEMENT_AUTO_DOWNLOAD_MODELS = os.getenv('ENGAGEMENT_AUTO_DOWNLOAD_MODELS', 'true').lower() == 'true'

# spaCy Configuration
ENGAGEMENT_SPACY_MODEL = os.getenv('ENGAGEMENT_SPACY_MODEL', 'en_core_web_sm')
ENGAGEMENT_SPACY_DISABLE_COMPONENTS = ['ner', 'parser']  # Disable unused components for performance

# BERTopic Configuration
ENGAGEMENT_BERTOPIC_LANGUAGE = os.getenv('ENGAGEMENT_BERTOPIC_LANGUAGE', 'english')
ENGAGEMENT_BERTOPIC_CALCULATE_PROBABILITIES = os.getenv('ENGAGEMENT_BERTOPIC_CALCULATE_PROBABILITIES', 'true').lower() == 'true'

# Validation Settings
def validate_scoring_weights() -> bool:
    """Validate that scoring weights sum to approximately 1.0"""
    total_weight = (
        ENGAGEMENT_EMOTION_WEIGHT +
        ENGAGEMENT_RHETORICAL_WEIGHT +
        ENGAGEMENT_TOPIC_TRANSITION_WEIGHT +
        ENGAGEMENT_KEYWORD_SIGNIFICANCE_WEIGHT
    )
    return abs(total_weight - 1.0) < 0.01

def get_engagement_config_summary() -> Dict[str, Any]:
    """Get a summary of current engagement scoring configuration"""
    return {
        'gpu_enabled': ENGAGEMENT_SCORING_USE_GPU,
        'gpu_fallback': ENGAGEMENT_SCORING_GPU_FALLBACK,
        'batch_size': ENGAGEMENT_SCORING_BATCH_SIZE,
        'emotion_model': ENGAGEMENT_EMOTION_MODEL,
        'sentence_model': ENGAGEMENT_SENTENCE_TRANSFORMER_MODEL,
        'scoring_weights': {
            'emotion': ENGAGEMENT_EMOTION_WEIGHT,
            'rhetorical': ENGAGEMENT_RHETORICAL_WEIGHT,
            'topic_transition': ENGAGEMENT_TOPIC_TRANSITION_WEIGHT,
            'keyword_significance': ENGAGEMENT_KEYWORD_SIGNIFICANCE_WEIGHT
        },
        'weights_valid': validate_scoring_weights(),
        'caching_enabled': ENGAGEMENT_ENABLE_CACHING,
        'parallel_processing': ENGAGEMENT_ENABLE_PARALLEL_PROCESSING,
        'scoring_enabled': ENGAGEMENT_SCORING_ENABLED
    }

# Export all settings
__all__ = [
    # GPU and Performance
    'ENGAGEMENT_SCORING_USE_GPU',
    'ENGAGEMENT_SCORING_GPU_FALLBACK', 
    'ENGAGEMENT_SCORING_BATCH_SIZE',
    'ENGAGEMENT_SCORING_MAX_BATCH_SIZE',
    
    # Models
    'ENGAGEMENT_EMOTION_MODEL',
    'ENGAGEMENT_EMOTION_MODEL_ALTERNATIVES',
    'ENGAGEMENT_TOPIC_MODEL_MIN_SIZE',
    'ENGAGEMENT_TOPIC_MODEL_COMPONENTS',
    'ENGAGEMENT_SENTENCE_TRANSFORMER_MODEL',
    'ENGAGEMENT_SPACY_MODEL',
    
    # Scoring Weights
    'ENGAGEMENT_EMOTION_WEIGHT',
    'ENGAGEMENT_RHETORICAL_WEIGHT',
    'ENGAGEMENT_TOPIC_TRANSITION_WEIGHT',
    'ENGAGEMENT_KEYWORD_SIGNIFICANCE_WEIGHT',
    
    # Thresholds
    'ENGAGEMENT_MIN_CONFIDENCE_THRESHOLD',
    'ENGAGEMENT_MIN_SEGMENT_DURATION',
    'ENGAGEMENT_MAX_SEGMENT_DURATION',
    'ENGAGEMENT_MIN_TEXT_LENGTH',
    
    # Caching
    'ENGAGEMENT_ENABLE_CACHING',
    'ENGAGEMENT_CACHE_TTL_HOURS',
    'ENGAGEMENT_CACHE_MAX_SIZE',
    'ENGAGEMENT_CACHE_DIR',
    
    # Processing
    'ENGAGEMENT_PROCESSING_TIMEOUT',
    'ENGAGEMENT_ENABLE_PARALLEL_PROCESSING',
    'ENGAGEMENT_MAX_WORKERS',
    
    # Feature-specific settings
    'ENGAGEMENT_EMOTION_THRESHOLD',
    'ENGAGEMENT_EMOTION_TOP_K',
    'ENGAGEMENT_RHETORICAL_PATTERNS',
    'ENGAGEMENT_TOPIC_SIMILARITY_THRESHOLD',
    'ENGAGEMENT_TOPIC_WINDOW_SIZE',
    'ENGAGEMENT_TOPIC_MIN_TRANSITION_SCORE',
    'ENGAGEMENT_KEYWORD_CONTEXT_WINDOW',
    'ENGAGEMENT_KEYWORD_TF_IDF_THRESHOLD',
    'ENGAGEMENT_KEYWORD_SEMANTIC_THRESHOLD',
    
    # Integration
    'ENGAGEMENT_SCORING_ENABLED',
    'ENGAGEMENT_SCORING_FALLBACK_TO_BASIC',
    'ENGAGEMENT_MODELS_CACHE_DIR',
    'ENGAGEMENT_AUTO_DOWNLOAD_MODELS',
    
    # Utilities
    'validate_scoring_weights',
    'get_engagement_config_summary'
]
