#!/usr/bin/env python3
"""
Highlight Extraction Module

This module provides comprehensive video highlight extraction functionality,
organized into logical components for maintainability and ease of use.

The module is structured as follows:
- core/: Core extraction engines and algorithms (including sophisticated engagement scoring)
- pipeline/: Pipeline task wrappers for integration with the main video processing pipeline
- utils/: Utility functions and classes for highlight processing
- jobs/: Job-based processing system for standalone highlight extraction
- config/: Configuration settings and parameters
- models/: Data models for engagement scoring and analysis

Enhanced Features:
- Sophisticated engagement scoring using local ML models
- Emotion detection with GoEmotions/DistilRoBERTa
- Rhetorical analysis using spaCy and pattern matching
- Topic transition detection using BERTopic
- Keyword significance using BERT embeddings and TF-IDF
- GPU acceleration with automatic CPU fallback
- Comprehensive caching and performance optimization
- Enterprise-grade reliability and backward compatibility

Usage:
    # For pipeline integration
    from highlight_extraction.pipeline import IntelligentHighlightsTask, EngagementHighlightsTask

    # For direct usage
    from highlight_extraction.core import EnhancedIntelligentExtractor, EngagementScorer

    # For configuration
    from highlight_extraction.models import ScoringConfig, EngagementScore
"""

# Core components
from .core.intelligent_extractor import IntelligentHighlightsExtractor
from .core.qa_extractor import QAHighlightsExtractor

# Enhanced engagement scoring (with graceful fallback)
try:
    from .core.enhanced_intelligent_extractor import EnhancedIntelligentExtractor
    from .core.engagement_scorer import EngagementScorer
    ENHANCED_SCORING_AVAILABLE = True
except ImportError as e:
    # Graceful fallback if dependencies are missing
    import logging
    logger = logging.getLogger(__name__)
    logger.warning(f"Enhanced engagement scoring not available: {e}")
    ENHANCED_SCORING_AVAILABLE = False

# Pipeline tasks
from .pipeline.intelligent_highlights_task import IntelligentHighlightsTask
from .pipeline.qa_highlights_task import QAHighlightsTask

# Enhanced pipeline task (with graceful fallback)
try:
    from .pipeline.engagement_highlights_task import EngagementHighlightsTask
    ENHANCED_PIPELINE_AVAILABLE = True
except ImportError:
    ENHANCED_PIPELINE_AVAILABLE = False

# Data models (with graceful fallback)
try:
    from .models.engagement_models import ScoringConfig, EngagementScore, EngagementType, ProcessingMode
    ENGAGEMENT_MODELS_AVAILABLE = True
except ImportError:
    ENGAGEMENT_MODELS_AVAILABLE = False

# Job management
from .jobs.manager import HighlightsJobManager
from .jobs.executor import HighlightsJobExecutor
from .jobs.models import HighlightsJob, HighlightsJobInput, JobStatus

# Configuration
from .config.settings import *

# Enhanced configuration (with graceful fallback)
try:
    from .config.engagement_settings import *
    ENHANCED_CONFIG_AVAILABLE = True
except ImportError:
    ENHANCED_CONFIG_AVAILABLE = False

# Version and feature flags
__version__ = "2.0.0"
__features__ = {
    'enhanced_scoring': ENHANCED_SCORING_AVAILABLE,
    'enhanced_pipeline': ENHANCED_PIPELINE_AVAILABLE,
    'engagement_models': ENGAGEMENT_MODELS_AVAILABLE,
    'enhanced_config': ENHANCED_CONFIG_AVAILABLE
}

# Export main components
__all__ = [
    # Core extractors
    'IntelligentHighlightsExtractor',
    'QAHighlightsExtractor',

    # Pipeline tasks
    'IntelligentHighlightsTask',
    'QAHighlightsTask',

    # Job management
    'HighlightsJobManager',
    'HighlightsJobExecutor',
    'HighlightsJob',
    'HighlightsJobInput',
    'JobStatus',

    # Feature flags
    '__version__',
    '__features__'
]

# Add enhanced components if available
if ENHANCED_SCORING_AVAILABLE:
    __all__.extend([
        'EnhancedIntelligentExtractor',
        'EngagementScorer'
    ])

if ENHANCED_PIPELINE_AVAILABLE:
    __all__.append('EngagementHighlightsTask')

if ENGAGEMENT_MODELS_AVAILABLE:
    __all__.extend([
        'ScoringConfig',
        'EngagementScore',
        'EngagementType',
        'ProcessingMode'
    ])


