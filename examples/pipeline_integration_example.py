#!/usr/bin/env python3
"""
Pipeline Integration Example for Enhanced Engagement Scoring

Demonstrates how to integrate the sophisticated engagement scoring system
into the existing video processing pipeline.
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import Dict, Any

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_sample_pipeline_data():
    """Create sample data that would come from previous pipeline stages"""
    
    # Sample transcription result (from TranscriptionEngine)
    transcription_result = {
        'status': 'completed',
        'job_id': 'sample_job_123',
        'transcript_path': 'sample_transcript.json',
        'output_dir': 'output/sample_job_123',
        'processing_time': 45.2,
        'metadata': {
            'duration': 180.0,
            'language': 'english',
            'segments_count': 8
        }
    }
    
    # Sample video ingestor result (from VideoIngestor)
    video_ingestor_result = {
        'status': 'completed',
        'job_id': 'sample_job_123',
        'video_path': 'sample_video.mp4',
        'optimized_path': 'output/sample_job_123/optimized_video.mp4',
        'metadata': {
            'duration': 180.0,
            'resolution': '1920x1080',
            'fps': 30.0,
            'file_size': 52428800
        }
    }
    
    # Sample transcript data
    transcript_data = {
        'duration': 180.0,
        'language': 'english',
        'segments': [
            {
                'id': 0,
                'start': 0.0,
                'end': 12.0,
                'text': 'Welcome to this absolutely incredible presentation about the future of artificial intelligence!',
                'avg_logprob': -0.15,
                'no_speech_prob': 0.01
            },
            {
                'id': 1,
                'start': 12.0,
                'end': 24.0,
                'text': 'What makes AI so revolutionary? Let me ask you this fundamental question.',
                'avg_logprob': -0.18,
                'no_speech_prob': 0.02
            },
            {
                'id': 2,
                'start': 24.0,
                'end': 36.0,
                'text': 'Machine learning algorithms are transforming every industry at an unprecedented pace.',
                'avg_logprob': -0.12,
                'no_speech_prob': 0.01
            },
            {
                'id': 3,
                'start': 36.0,
                'end': 48.0,
                'text': 'This is truly mind-blowing! The implications are absolutely extraordinary.',
                'avg_logprob': -0.14,
                'no_speech_prob': 0.01
            },
            {
                'id': 4,
                'start': 48.0,
                'end': 60.0,
                'text': 'How do neural networks process information? What are the key mechanisms?',
                'avg_logprob': -0.16,
                'no_speech_prob': 0.02
            },
            {
                'id': 5,
                'start': 60.0,
                'end': 72.0,
                'text': 'Deep learning represents a paradigm shift in computational thinking and problem solving.',
                'avg_logprob': -0.19,
                'no_speech_prob': 0.01
            },
            {
                'id': 6,
                'start': 72.0,
                'end': 84.0,
                'text': 'The future is here! Are you prepared for this technological revolution?',
                'avg_logprob': -0.13,
                'no_speech_prob': 0.01
            },
            {
                'id': 7,
                'start': 84.0,
                'end': 96.0,
                'text': 'Let me demonstrate practical applications with concrete, real-world examples.',
                'avg_logprob': -0.17,
                'no_speech_prob': 0.01
            }
        ]
    }
    
    # Pipeline parameters
    params = {
        'keywords': ['AI', 'artificial intelligence', 'machine learning', 'neural networks', 'deep learning', 'revolutionary'],
        'target_length': 45,  # 45 seconds of highlights
        'extract_qa_pairs': True,
        'extract_segments': True,
        'engagement_scoring_enabled': True
    }
    
    return transcription_result, video_ingestor_result, transcript_data, params


def demonstrate_engagement_task_integration():
    """Demonstrate integration with EngagementHighlightsTask"""
    logger.info("🔧 Demonstrating EngagementHighlightsTask Integration")
    logger.info("-" * 60)
    
    try:
        from highlight_extraction.pipeline.engagement_highlights_task import EngagementHighlightsTask
        from highlight_extraction.models.engagement_models import ScoringConfig
        import tempfile
        
        # Create sample data
        transcription_result, video_ingestor_result, transcript_data, params = create_sample_pipeline_data()
        
        # Create temporary files
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create transcript file
            transcript_path = os.path.join(temp_dir, 'transcript.json')
            with open(transcript_path, 'w') as f:
                json.dump(transcript_data, f, indent=2)
            
            # Update paths
            transcription_result['transcript_path'] = transcript_path
            transcription_result['output_dir'] = temp_dir
            video_ingestor_result['video_path'] = 'sample_video.mp4'  # Mock path
            video_ingestor_result['optimized_path'] = 'sample_video.mp4'  # Mock path
            
            # Initialize task with CPU-only config for demo
            config = ScoringConfig(use_gpu=False, batch_size=4)
            task = EngagementHighlightsTask(config)
            
            logger.info("Running engagement highlights extraction...")
            
            # Run the task
            result = task.run(
                job_id='sample_job_123',
                transcription_result=transcription_result,
                video_ingestor_result=video_ingestor_result,
                params=params
            )
            
            # Display results
            logger.info(f"✅ Task completed with status: {result['status']}")
            
            if result['status'] == 'completed':
                logger.info(f"📊 Results:")
                logger.info(f"  Highlights generated: {result['highlights_count']}")
                logger.info(f"  Total duration: {result['total_duration']:.1f}s")
                logger.info(f"  Target duration: {result['target_duration']}s")
                logger.info(f"  Processing time: {result['processing_time']:.2f}s")
                logger.info(f"  Enhanced scoring used: {result['enhanced_scoring_used']}")
                
                # Show sample highlights
                highlights = result.get('highlights', [])
                logger.info(f"\n🎬 Sample Highlights:")
                for i, highlight in enumerate(highlights[:3]):  # Show first 3
                    logger.info(f"  {i+1}. {highlight['start_time']:.1f}s-{highlight['end_time']:.1f}s "
                              f"(score: {highlight['score']:.3f})")
                    logger.info(f"     {highlight['text'][:80]}...")
                
                # Show engagement analysis
                engagement_analysis = result.get('engagement_analysis', {})
                if engagement_analysis:
                    logger.info(f"\n🧠 Engagement Analysis:")
                    logger.info(f"  Total segments: {engagement_analysis.get('total_segments', 0)}")
                    logger.info(f"  High engagement: {engagement_analysis.get('high_engagement_count', 0)}")
                    
                    score_dist = engagement_analysis.get('score_distribution', {})
                    logger.info(f"  Score distribution:")
                    logger.info(f"    Low: {score_dist.get('low_engagement', 0)}")
                    logger.info(f"    Medium: {score_dist.get('medium_engagement', 0)}")
                    logger.info(f"    High: {score_dist.get('high_engagement', 0)}")
            
            elif result['status'] == 'skipped':
                logger.warning(f"Task was skipped: {result.get('reason', 'Unknown')}")
            
            else:
                logger.error(f"Task failed: {result.get('error', 'Unknown error')}")
            
            return result['status'] == 'completed'
    
    except ImportError as e:
        logger.error(f"Engagement task not available: {e}")
        return False
    except Exception as e:
        logger.error(f"Error in task integration: {e}")
        return False


def demonstrate_sequential_pipeline():
    """Demonstrate how engagement scoring fits in a sequential pipeline"""
    logger.info("\n🔄 Demonstrating Sequential Pipeline Integration")
    logger.info("-" * 60)
    
    try:
        # Import pipeline components
        from highlight_extraction.pipeline.engagement_highlights_task import EngagementHighlightsTask
        from highlight_extraction.models.engagement_models import ScoringConfig
        
        # Simulate pipeline stages
        job_id = 'demo_pipeline_job'
        
        logger.info("Pipeline Stage 1: Video Ingestion")
        logger.info("  ✅ Video ingested and optimized")
        
        logger.info("Pipeline Stage 2: Transcription")
        logger.info("  ✅ Audio transcribed with Whisper")
        
        logger.info("Pipeline Stage 3: Enhanced Engagement Scoring")
        
        # Create sample data
        transcription_result, video_ingestor_result, transcript_data, params = create_sample_pipeline_data()
        
        # Initialize engagement task
        config = ScoringConfig(
            use_gpu=False,
            batch_size=4,
            emotion_weight=0.3,
            rhetorical_weight=0.25,
            topic_transition_weight=0.25,
            keyword_significance_weight=0.2
        )
        
        engagement_task = EngagementHighlightsTask(config)
        
        # Check if task is available
        if not engagement_task.extractor:
            logger.warning("  ⚠️  Engagement scoring not available, would skip to next stage")
            return False
        
        logger.info("  🧠 Running sophisticated engagement analysis...")
        logger.info("    - Emotion detection with GoEmotions")
        logger.info("    - Rhetorical pattern analysis with spaCy")
        logger.info("    - Topic transition detection with BERTopic")
        logger.info("    - Keyword significance with BERT embeddings")
        
        # Simulate the task execution
        import tempfile
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create transcript file
            transcript_path = os.path.join(temp_dir, 'transcript.json')
            with open(transcript_path, 'w') as f:
                json.dump(transcript_data, f)
            
            transcription_result['transcript_path'] = transcript_path
            transcription_result['output_dir'] = temp_dir
            video_ingestor_result['video_path'] = 'mock_video.mp4'
            
            # Run engagement scoring
            engagement_result = engagement_task.run(
                job_id=job_id,
                transcription_result=transcription_result,
                video_ingestor_result=video_ingestor_result,
                params=params
            )
            
            if engagement_result['status'] == 'completed':
                logger.info("  ✅ Engagement scoring completed successfully")
                logger.info(f"    Generated {engagement_result['highlights_count']} high-engagement segments")
                
                logger.info("Pipeline Stage 4: Clip Rendering")
                logger.info("  ✅ Video clips rendered from engagement highlights")
                
                logger.info("Pipeline Stage 5: Reframing")
                logger.info("  ✅ Clips reframed for vertical video")
                
                logger.info("Pipeline Stage 6: Caption Composition")
                logger.info("  ✅ Captions added to final clips")
                
                logger.info("\n🎉 Complete Pipeline Success!")
                logger.info(f"Final output: {engagement_result['highlights_count']} optimized video clips")
                
                return True
            else:
                logger.error(f"  ❌ Engagement scoring failed: {engagement_result.get('error', 'Unknown')}")
                return False
    
    except Exception as e:
        logger.error(f"Pipeline demonstration failed: {e}")
        return False


def show_configuration_options():
    """Show configuration options for engagement scoring"""
    logger.info("\n⚙️  Configuration Options")
    logger.info("-" * 60)
    
    try:
        from highlight_extraction.config.engagement_settings import get_engagement_config_summary
        
        config_summary = get_engagement_config_summary()
        
        logger.info("Current Configuration:")
        logger.info(f"  GPU enabled: {config_summary.get('gpu_enabled', False)}")
        logger.info(f"  GPU fallback: {config_summary.get('gpu_fallback', False)}")
        logger.info(f"  Batch size: {config_summary.get('batch_size', 'N/A')}")
        logger.info(f"  Emotion model: {config_summary.get('emotion_model', 'N/A')}")
        logger.info(f"  Sentence model: {config_summary.get('sentence_model', 'N/A')}")
        
        weights = config_summary.get('scoring_weights', {})
        logger.info(f"  Scoring weights:")
        logger.info(f"    Emotion: {weights.get('emotion', 0.0):.2f}")
        logger.info(f"    Rhetorical: {weights.get('rhetorical', 0.0):.2f}")
        logger.info(f"    Topic transition: {weights.get('topic_transition', 0.0):.2f}")
        logger.info(f"    Keyword significance: {weights.get('keyword_significance', 0.0):.2f}")
        
        logger.info(f"  Weights valid: {config_summary.get('weights_valid', False)}")
        logger.info(f"  Caching enabled: {config_summary.get('caching_enabled', False)}")
        logger.info(f"  Parallel processing: {config_summary.get('parallel_processing', False)}")
        
        logger.info("\nEnvironment Variables for Customization:")
        logger.info("  ENGAGEMENT_SCORING_USE_GPU=true/false")
        logger.info("  ENGAGEMENT_SCORING_BATCH_SIZE=32")
        logger.info("  ENGAGEMENT_EMOTION_WEIGHT=0.3")
        logger.info("  ENGAGEMENT_RHETORICAL_WEIGHT=0.25")
        logger.info("  ENGAGEMENT_TOPIC_WEIGHT=0.25")
        logger.info("  ENGAGEMENT_KEYWORD_WEIGHT=0.2")
        logger.info("  ENGAGEMENT_MIN_CONFIDENCE=0.3")
        logger.info("  ENGAGEMENT_ENABLE_CACHING=true")
        
        return True
        
    except Exception as e:
        logger.error(f"Error showing configuration: {e}")
        return False


def main():
    """Main demonstration function"""
    logger.info("🚀 Enhanced Engagement Scoring Pipeline Integration Demo")
    logger.info("=" * 70)
    
    # Run demonstrations
    demos = [
        ("Engagement Task Integration", demonstrate_engagement_task_integration),
        ("Sequential Pipeline Flow", demonstrate_sequential_pipeline),
        ("Configuration Options", show_configuration_options)
    ]
    
    results = []
    for demo_name, demo_func in demos:
        try:
            success = demo_func()
            results.append((demo_name, success))
        except Exception as e:
            logger.error(f"Demo '{demo_name}' failed: {e}")
            results.append((demo_name, False))
    
    # Summary
    logger.info(f"\n{'='*70}")
    logger.info("📋 Integration Demo Results:")
    for demo_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        logger.info(f"  {status} {demo_name}")
    
    successful_demos = sum(1 for _, success in results if success)
    logger.info(f"\n🎯 {successful_demos}/{len(results)} integration demos completed successfully")
    
    if successful_demos > 0:
        logger.info("\n🎉 Integration demos show the engagement scoring system is ready for production!")
        logger.info("\nNext Steps:")
        logger.info("1. Install dependencies: pip install -r requirements.txt")
        logger.info("2. Setup models: python scripts/setup_engagement_models.py")
        logger.info("3. Configure environment variables in .env file")
        logger.info("4. Integrate EngagementHighlightsTask into your pipeline")
        logger.info("5. Run tests: python -m pytest tests/test_engagement_scoring.py")
    else:
        logger.warning("⚠️  Integration demos failed. Check dependencies and configuration.")
    
    return successful_demos > 0


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
