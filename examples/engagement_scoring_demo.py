#!/usr/bin/env python3
"""
Enhanced Engagement Scoring System Demo

Demonstrates the sophisticated engagement scoring capabilities including:
- Emotion detection using GoEmotions/DistilRoBERTa
- Rhetorical analysis using spaCy and pattern matching
- Topic transition detection using BERTopic
- Keyword significance using BERT embeddings
- GPU acceleration with CPU fallback
- Comprehensive performance metrics and analysis
"""

import os
import sys
import json
import time
import logging
from pathlib import Path
from typing import Dict, Any, List

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def load_sample_transcript() -> Dict[str, Any]:
    """Load sample transcript data for demonstration"""
    return {
        'duration': 180.0,
        'language': 'english',
        'segments': [
            {
                'id': 0,
                'start': 0.0,
                'end': 8.0,
                'text': 'Welcome everyone to this absolutely incredible presentation about artificial intelligence!',
                'avg_logprob': -0.2,
                'no_speech_prob': 0.01
            },
            {
                'id': 1,
                'start': 8.0,
                'end': 16.0,
                'text': 'What makes AI so fascinating? Let me ask you this important question.',
                'avg_logprob': -0.15,
                'no_speech_prob': 0.02
            },
            {
                'id': 2,
                'start': 16.0,
                'end': 24.0,
                'text': 'Machine learning algorithms can process vast amounts of data very quickly.',
                'avg_logprob': -0.18,
                'no_speech_prob': 0.01
            },
            {
                'id': 3,
                'start': 24.0,
                'end': 32.0,
                'text': 'This is truly revolutionary! The implications are absolutely mind-blowing.',
                'avg_logprob': -0.12,
                'no_speech_prob': 0.01
            },
            {
                'id': 4,
                'start': 32.0,
                'end': 40.0,
                'text': 'How do neural networks actually work? What are the key components?',
                'avg_logprob': -0.16,
                'no_speech_prob': 0.02
            },
            {
                'id': 5,
                'start': 40.0,
                'end': 48.0,
                'text': 'Deep learning represents a fundamental shift in computational approaches.',
                'avg_logprob': -0.19,
                'no_speech_prob': 0.01
            },
            {
                'id': 6,
                'start': 48.0,
                'end': 56.0,
                'text': 'The future of technology is here! Are you ready for this transformation?',
                'avg_logprob': -0.14,
                'no_speech_prob': 0.01
            },
            {
                'id': 7,
                'start': 56.0,
                'end': 64.0,
                'text': 'Let me demonstrate some practical applications with real examples.',
                'avg_logprob': -0.17,
                'no_speech_prob': 0.01
            }
        ]
    }


def demonstrate_basic_scoring():
    """Demonstrate basic engagement scoring functionality"""
    logger.info("🎯 Demonstrating Basic Engagement Scoring")
    logger.info("-" * 50)
    
    try:
        from highlight_extraction.core.engagement_scorer import EngagementScorer
        from highlight_extraction.models.engagement_models import ScoringConfig
        
        # Create configuration
        config = ScoringConfig(
            use_gpu=False,  # Use CPU for demo
            batch_size=4,
            emotion_weight=0.3,
            rhetorical_weight=0.25,
            topic_transition_weight=0.25,
            keyword_significance_weight=0.2
        )
        
        # Initialize scorer
        logger.info("Initializing EngagementScorer...")
        scorer = EngagementScorer(config)
        
        # Load sample data
        transcript_data = load_sample_transcript()
        segments = transcript_data['segments']
        keywords = ['AI', 'artificial intelligence', 'machine learning', 'neural networks', 'technology']
        
        logger.info(f"Processing {len(segments)} segments with {len(keywords)} keywords")
        
        # Score segments
        start_time = time.time()
        engagement_scores = scorer.score_segments(segments, keywords)
        processing_time = time.time() - start_time
        
        logger.info(f"✅ Scored {len(engagement_scores)} segments in {processing_time:.2f}s")
        
        # Display results
        logger.info("\n📊 Engagement Scores:")
        for i, score in enumerate(engagement_scores[:5]):  # Show first 5
            logger.info(f"  Segment {i+1} ({score.start_time:.1f}s-{score.end_time:.1f}s):")
            logger.info(f"    Confidence: {score.confidence_score:.3f}")
            logger.info(f"    Emotion: {score.emotion_score:.3f}")
            logger.info(f"    Rhetorical: {score.rhetorical_score:.3f}")
            logger.info(f"    Topic Transition: {score.topic_transition_score:.3f}")
            logger.info(f"    Keyword Significance: {score.keyword_significance_score:.3f}")
            logger.info(f"    Text: {score.text[:60]}...")
            logger.info("")
        
        # Get high-engagement segments
        high_engagement = scorer.get_high_engagement_segments(engagement_scores, threshold=0.5)
        logger.info(f"🔥 Found {len(high_engagement)} high-engagement segments (threshold: 0.5)")
        
        # Analyze distribution
        analysis = scorer.analyze_engagement_distribution(engagement_scores)
        logger.info(f"\n📈 Engagement Distribution:")
        logger.info(f"  Total segments: {analysis['total_segments']}")
        logger.info(f"  High engagement: {analysis['high_engagement_count']}")
        logger.info(f"  Score distribution: {analysis['score_distribution']}")
        
        # Performance metrics
        metrics = scorer.get_performance_metrics()
        logger.info(f"\n⚡ Performance Metrics:")
        logger.info(f"  Device used: {metrics['device_used']}")
        logger.info(f"  Processing mode: {metrics['processing_mode']}")
        logger.info(f"  Total processing time: {metrics['total_processing_time']:.3f}s")
        logger.info(f"  Cache hit rate: {metrics['cache_hit_rate']:.2%}")
        
        return True
        
    except ImportError as e:
        logger.error(f"Enhanced scoring not available: {e}")
        return False
    except Exception as e:
        logger.error(f"Error in basic scoring demo: {e}")
        return False


def demonstrate_enhanced_extraction():
    """Demonstrate enhanced intelligent extraction"""
    logger.info("\n🚀 Demonstrating Enhanced Intelligent Extraction")
    logger.info("-" * 50)
    
    try:
        from highlight_extraction.core.enhanced_intelligent_extractor import EnhancedIntelligentExtractor
        from highlight_extraction.models.engagement_models import ScoringConfig
        
        # Create configuration
        config = ScoringConfig(use_gpu=False, batch_size=4)
        
        # Initialize extractor
        logger.info("Initializing EnhancedIntelligentExtractor...")
        extractor = EnhancedIntelligentExtractor(config)
        
        if not extractor.is_enhanced_scoring_available():
            logger.warning("Enhanced scoring not available, falling back to basic extraction")
        
        # Load sample data
        transcript_data = load_sample_transcript()
        keywords = ['AI', 'artificial intelligence', 'revolutionary', 'transformation']
        
        # Create temporary output directory
        import tempfile
        with tempfile.TemporaryDirectory() as temp_dir:
            # Mock video path (not actually used in this demo)
            video_path = "sample_video.mp4"
            
            logger.info(f"Extracting highlights with target length: 30s")
            
            # Extract highlights
            start_time = time.time()
            try:
                result = extractor.extract(
                    transcription_data=transcript_data,
                    video_path=video_path,
                    keywords=keywords,
                    target_length=30,
                    output_dir=temp_dir
                )
                processing_time = time.time() - start_time
                
                logger.info(f"✅ Extraction completed in {processing_time:.2f}s")
                
                # Display results
                highlights = result.get('highlights', [])
                logger.info(f"\n🎬 Generated {len(highlights)} highlights:")
                
                total_duration = 0
                for i, highlight in enumerate(highlights):
                    duration = highlight['duration']
                    total_duration += duration
                    logger.info(f"  Highlight {i+1}: {highlight['start_time']:.1f}s-{highlight['end_time']:.1f}s "
                              f"({duration:.1f}s, score: {highlight['score']:.3f})")
                    logger.info(f"    Text: {highlight['text'][:80]}...")
                
                logger.info(f"\n📊 Summary:")
                logger.info(f"  Total duration: {total_duration:.1f}s")
                logger.info(f"  Target duration: 30s")
                logger.info(f"  Efficiency: {(total_duration/30)*100:.1f}%")
                
                # Engagement analysis
                engagement_analysis = result.get('engagement_analysis', {})
                if engagement_analysis:
                    logger.info(f"\n🧠 Engagement Analysis:")
                    logger.info(f"  Total segments analyzed: {engagement_analysis.get('total_segments', 0)}")
                    logger.info(f"  High engagement segments: {engagement_analysis.get('high_engagement_count', 0)}")
                
                return True
                
            except Exception as e:
                logger.error(f"Extraction failed: {e}")
                # Try basic extraction as fallback
                logger.info("Attempting basic extraction...")
                return False
        
    except ImportError as e:
        logger.error(f"Enhanced extraction not available: {e}")
        return False
    except Exception as e:
        logger.error(f"Error in enhanced extraction demo: {e}")
        return False


def demonstrate_pipeline_integration():
    """Demonstrate pipeline task integration"""
    logger.info("\n🔧 Demonstrating Pipeline Integration")
    logger.info("-" * 50)
    
    try:
        from highlight_extraction.pipeline.engagement_highlights_task import EngagementHighlightsTask
        from highlight_extraction.models.engagement_models import ScoringConfig
        
        # Create configuration
        config = ScoringConfig(use_gpu=False)
        
        # Initialize task
        logger.info("Initializing EngagementHighlightsTask...")
        task = EngagementHighlightsTask(config)
        
        # Get task info
        info = task.get_task_info()
        logger.info(f"Task name: {info['task_name']}")
        logger.info(f"Requires GPU: {info['requires_gpu']}")
        logger.info(f"Enhanced scoring available: {info['enhanced_scoring_available']}")
        
        # Validate dependencies
        dependencies = task.validate_dependencies()
        logger.info(f"\n📦 Dependencies:")
        for dep, available in dependencies.items():
            status = "✅" if available else "❌"
            logger.info(f"  {status} {dep}")
        
        # Show configuration summary
        config_summary = info.get('config_summary', {})
        logger.info(f"\n⚙️  Configuration:")
        logger.info(f"  GPU enabled: {config_summary.get('gpu_enabled', False)}")
        logger.info(f"  Batch size: {config_summary.get('batch_size', 'N/A')}")
        logger.info(f"  Scoring enabled: {config_summary.get('scoring_enabled', False)}")
        
        return True
        
    except ImportError as e:
        logger.error(f"Pipeline integration not available: {e}")
        return False
    except Exception as e:
        logger.error(f"Error in pipeline integration demo: {e}")
        return False


def main():
    """Main demo function"""
    logger.info("🎉 Enhanced Engagement Scoring System Demo")
    logger.info("=" * 60)
    
    # Check if enhanced scoring is available
    try:
        import highlight_extraction
        features = highlight_extraction.__features__
        logger.info(f"Highlight extraction version: {highlight_extraction.__version__}")
        logger.info(f"Available features: {features}")
        
        if not features.get('enhanced_scoring', False):
            logger.warning("Enhanced scoring features not available")
            logger.info("Please run: python scripts/setup_engagement_models.py")
            return False
    except ImportError:
        logger.error("Highlight extraction module not available")
        return False
    
    # Run demonstrations
    demos = [
        ("Basic Engagement Scoring", demonstrate_basic_scoring),
        ("Enhanced Intelligent Extraction", demonstrate_enhanced_extraction),
        ("Pipeline Integration", demonstrate_pipeline_integration)
    ]
    
    results = []
    for demo_name, demo_func in demos:
        try:
            logger.info(f"\n{'='*60}")
            success = demo_func()
            results.append((demo_name, success))
        except Exception as e:
            logger.error(f"Demo '{demo_name}' failed: {e}")
            results.append((demo_name, False))
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("📋 Demo Results Summary:")
    for demo_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        logger.info(f"  {status} {demo_name}")
    
    successful_demos = sum(1 for _, success in results if success)
    logger.info(f"\n🎯 {successful_demos}/{len(results)} demos completed successfully")
    
    if successful_demos == len(results):
        logger.info("🎉 All demos passed! The engagement scoring system is working correctly.")
    else:
        logger.warning("⚠️  Some demos failed. Check the logs above for details.")
    
    return successful_demos == len(results)


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
