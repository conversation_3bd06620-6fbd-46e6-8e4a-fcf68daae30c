#!/usr/bin/env python3
"""
Setup Script for Engagement Scoring Models

Downloads and configures all required ML models for the sophisticated engagement scoring system.
"""

import os
import sys
import logging
import subprocess
from pathlib import Path
from typing import List, Dict, Any

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_dependencies() -> Dict[str, bool]:
    """Check if required dependencies are installed"""
    dependencies = {
        'torch': False,
        'transformers': False,
        'sentence_transformers': False,
        'spacy': False,
        'bertopic': False,
        'umap-learn': False,
        'hdbscan': False,
        'scikit-learn': False,
        'numpy': False
    }
    
    for dep in dependencies:
        try:
            __import__(dep.replace('-', '_'))
            dependencies[dep] = True
            logger.info(f"✅ {dep} is available")
        except ImportError:
            logger.warning(f"❌ {dep} is not available")
    
    return dependencies


def install_missing_dependencies(missing_deps: List[str]) -> bool:
    """Install missing dependencies using pip"""
    if not missing_deps:
        logger.info("All dependencies are already installed")
        return True
    
    logger.info(f"Installing missing dependencies: {missing_deps}")
    
    try:
        cmd = [sys.executable, '-m', 'pip', 'install'] + missing_deps
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info("Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install dependencies: {e}")
        logger.error(f"Error output: {e.stderr}")
        return False


def download_spacy_model() -> bool:
    """Download spaCy English model"""
    try:
        logger.info("Downloading spaCy English model...")
        cmd = [sys.executable, '-m', 'spacy', 'download', 'en_core_web_sm']
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info("✅ spaCy model downloaded successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to download spaCy model: {e}")
        return False


def download_transformers_models() -> bool:
    """Download and cache Transformers models"""
    models_to_download = [
        'j-hartmann/emotion-english-distilroberta-base',
        'cardiffnlp/twitter-roberta-base-emotion',
        'SamLowe/roberta-base-go_emotions'
    ]
    
    try:
        from transformers import AutoTokenizer, AutoModelForSequenceClassification
        
        for model_name in models_to_download:
            try:
                logger.info(f"Downloading {model_name}...")
                tokenizer = AutoTokenizer.from_pretrained(model_name)
                model = AutoModelForSequenceClassification.from_pretrained(model_name)
                logger.info(f"✅ {model_name} downloaded successfully")
            except Exception as e:
                logger.warning(f"Failed to download {model_name}: {e}")
        
        return True
    except ImportError:
        logger.error("Transformers not available, skipping model downloads")
        return False


def download_sentence_transformers_models() -> bool:
    """Download and cache Sentence Transformers models"""
    models_to_download = [
        'all-MiniLM-L6-v2',
        'all-mpnet-base-v2',
        'paraphrase-MiniLM-L6-v2'
    ]
    
    try:
        from sentence_transformers import SentenceTransformer
        
        for model_name in models_to_download:
            try:
                logger.info(f"Downloading {model_name}...")
                model = SentenceTransformer(model_name)
                logger.info(f"✅ {model_name} downloaded successfully")
            except Exception as e:
                logger.warning(f"Failed to download {model_name}: {e}")
        
        return True
    except ImportError:
        logger.error("Sentence Transformers not available, skipping model downloads")
        return False


def test_engagement_scoring() -> bool:
    """Test the engagement scoring system"""
    try:
        logger.info("Testing engagement scoring system...")
        
        # Import with fallback
        try:
            from highlight_extraction.core.engagement_scorer import EngagementScorer
            from highlight_extraction.models.engagement_models import ScoringConfig
        except ImportError as e:
            logger.error(f"Failed to import engagement scoring components: {e}")
            return False
        
        # Create test configuration
        config = ScoringConfig(
            use_gpu=False,  # Use CPU for testing
            batch_size=2
        )
        
        # Initialize scorer
        scorer = EngagementScorer(config)
        
        # Test with sample data
        test_segments = [
            {
                'id': 0,
                'start': 0.0,
                'end': 5.0,
                'text': 'This is an amazing and exciting presentation!'
            },
            {
                'id': 1,
                'start': 5.0,
                'end': 10.0,
                'text': 'What do you think about this approach?'
            }
        ]
        
        test_keywords = ['amazing', 'exciting', 'approach']
        
        # Score segments
        scores = scorer.score_segments(test_segments, test_keywords)
        
        if scores:
            logger.info(f"✅ Engagement scoring test successful - generated {len(scores)} scores")
            
            # Print sample results
            for i, score in enumerate(scores[:2]):
                logger.info(f"  Score {i+1}: confidence={score.confidence_score:.3f}, "
                          f"emotion={score.emotion_score:.3f}, "
                          f"rhetorical={score.rhetorical_score:.3f}")
            
            return True
        else:
            logger.warning("Engagement scoring returned no results")
            return False
            
    except Exception as e:
        logger.error(f"Engagement scoring test failed: {e}")
        return False


def create_environment_file() -> bool:
    """Create a sample .env file with engagement scoring configuration"""
    env_content = """# Engagement Scoring Configuration

# GPU and Performance Settings
ENGAGEMENT_SCORING_USE_GPU=true
ENGAGEMENT_SCORING_GPU_FALLBACK=true
ENGAGEMENT_SCORING_BATCH_SIZE=32
ENGAGEMENT_SCORING_MAX_BATCH_SIZE=128

# Model Configuration
ENGAGEMENT_EMOTION_MODEL=j-hartmann/emotion-english-distilroberta-base
ENGAGEMENT_SENTENCE_MODEL=all-MiniLM-L6-v2

# Scoring Weights (must sum to 1.0)
ENGAGEMENT_EMOTION_WEIGHT=0.3
ENGAGEMENT_RHETORICAL_WEIGHT=0.25
ENGAGEMENT_TOPIC_WEIGHT=0.25
ENGAGEMENT_KEYWORD_WEIGHT=0.2

# Quality Thresholds
ENGAGEMENT_MIN_CONFIDENCE=0.3
ENGAGEMENT_MIN_DURATION=2.0
ENGAGEMENT_MAX_DURATION=30.0

# Caching Configuration
ENGAGEMENT_ENABLE_CACHING=true
ENGAGEMENT_CACHE_TTL_HOURS=24
ENGAGEMENT_CACHE_MAX_SIZE=1000

# Processing Configuration
ENGAGEMENT_PROCESSING_TIMEOUT=300.0
ENGAGEMENT_PARALLEL_PROCESSING=true
ENGAGEMENT_MAX_WORKERS=4

# Integration Settings
ENGAGEMENT_SCORING_ENABLED=true
ENGAGEMENT_SCORING_FALLBACK_TO_BASIC=true
"""
    
    env_file_path = project_root / '.env.engagement'
    
    try:
        with open(env_file_path, 'w') as f:
            f.write(env_content)
        logger.info(f"✅ Sample environment file created: {env_file_path}")
        logger.info("You can copy these settings to your main .env file")
        return True
    except Exception as e:
        logger.error(f"Failed to create environment file: {e}")
        return False


def main():
    """Main setup function"""
    logger.info("🚀 Setting up Enhanced Engagement Scoring System")
    logger.info("=" * 60)
    
    # Check dependencies
    logger.info("1. Checking dependencies...")
    deps = check_dependencies()
    missing_deps = [dep for dep, available in deps.items() if not available]
    
    if missing_deps:
        logger.info(f"Missing dependencies: {missing_deps}")
        if input("Install missing dependencies? (y/n): ").lower() == 'y':
            if not install_missing_dependencies(missing_deps):
                logger.error("Failed to install dependencies. Exiting.")
                return False
        else:
            logger.warning("Skipping dependency installation")
    
    # Download spaCy model
    logger.info("\n2. Setting up spaCy model...")
    if deps.get('spacy', False) or 'spacy' not in missing_deps:
        download_spacy_model()
    
    # Download Transformers models
    logger.info("\n3. Setting up Transformers models...")
    if deps.get('transformers', False) or 'transformers' not in missing_deps:
        download_transformers_models()
    
    # Download Sentence Transformers models
    logger.info("\n4. Setting up Sentence Transformers models...")
    if deps.get('sentence_transformers', False) or 'sentence-transformers' not in missing_deps:
        download_sentence_transformers_models()
    
    # Test the system
    logger.info("\n5. Testing engagement scoring system...")
    test_success = test_engagement_scoring()
    
    # Create environment file
    logger.info("\n6. Creating configuration file...")
    create_environment_file()
    
    # Summary
    logger.info("\n" + "=" * 60)
    if test_success:
        logger.info("🎉 Enhanced Engagement Scoring System setup completed successfully!")
        logger.info("\nNext steps:")
        logger.info("1. Review the configuration in .env.engagement")
        logger.info("2. Copy relevant settings to your main .env file")
        logger.info("3. Run the test suite: python -m pytest tests/test_engagement_scoring.py")
        logger.info("4. Integrate with your pipeline using EngagementHighlightsTask")
    else:
        logger.warning("⚠️  Setup completed with some issues. Check the logs above.")
        logger.info("The system may still work with reduced functionality.")
    
    return test_success


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
