#!/usr/bin/env python3
"""
Basic import test for engagement scoring system
"""

import sys
import os

print("Starting basic import test...")
print(f"Python version: {sys.version}")
print(f"Current directory: {os.getcwd()}")

try:
    print("Testing dataclass import...")
    from dataclasses import dataclass
    print("✅ dataclass imported")
    
    print("Testing enum import...")
    from enum import Enum
    print("✅ enum imported")
    
    print("Testing typing import...")
    from typing import Dict, Any, List, Optional
    print("✅ typing imported")
    
    print("Testing engagement models...")
    from highlight_extraction.models.engagement_models import EngagementType
    print("✅ EngagementType imported")
    
    print("Testing ScoringConfig...")
    from highlight_extraction.models.engagement_models import ScoringConfig
    print("✅ ScoringConfig imported")
    
    config = ScoringConfig(use_gpu=False)
    print(f"✅ ScoringConfig created: {config.use_gpu}")
    
    print("Testing EngagementScore...")
    from highlight_extraction.models.engagement_models import EngagementScore
    print("✅ EngagementScore imported")
    
    score = EngagementScore(
        start_time=0.0,
        end_time=5.0,
        engagement_type="emotion",
        confidence_score=0.8
    )
    print(f"✅ EngagementScore created: {score.duration}s")
    
    print("All basic imports successful!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
