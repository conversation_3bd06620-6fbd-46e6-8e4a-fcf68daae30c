#!/usr/bin/env python3

print("Testing direct imports (bypassing __init__.py)...")

try:
    # Test direct import of models
    print("Testing engagement models...")
    from highlight_extraction.models.engagement_models import (
        ScoringConfig, EngagementScore, EngagementType, ProcessingMode
    )
    print("✅ All engagement models imported successfully")
    
    # Test config creation
    config = ScoringConfig(
        use_gpu=False,
        batch_size=16,
        emotion_weight=0.3,
        rhetorical_weight=0.25,
        topic_transition_weight=0.25,
        keyword_significance_weight=0.2
    )
    print(f"✅ ScoringConfig created: GPU={config.use_gpu}, batch={config.batch_size}")
    
    # Test score creation
    score = EngagementScore(
        start_time=10.0,
        end_time=15.0,
        engagement_type=EngagementType.EMOTION.value,
        confidence_score=0.85,
        emotion_score=0.9,
        rhetorical_score=0.7,
        text="This is an amazing and exciting moment!"
    )
    print(f"✅ EngagementScore created: {score.start_time}s-{score.end_time}s")
    print(f"   Confidence: {score.confidence_score}, Duration: {score.duration}s")
    print(f"   Emotion: {score.emotion_score}, Rhetorical: {score.rhetorical_score}")
    
    # Test serialization
    score_dict = score.to_dict()
    config_dict = config.to_dict()
    print(f"✅ Serialization works: score={len(score_dict)} keys, config={len(config_dict)} keys")
    
    # Test settings
    print("\nTesting engagement settings...")
    from highlight_extraction.config.engagement_settings import (
        get_engagement_config_summary, validate_scoring_weights
    )
    
    summary = get_engagement_config_summary()
    weights_valid = validate_scoring_weights()
    print(f"✅ Settings loaded: {len(summary)} config items, weights_valid={weights_valid}")
    
    # Test engagement scorer (basic import)
    print("\nTesting engagement scorer import...")
    try:
        from highlight_extraction.core.engagement_scorer import EngagementScorer
        print("✅ EngagementScorer imported successfully")
        
        # Try to create scorer with CPU-only config
        scorer = EngagementScorer(config)
        print(f"✅ EngagementScorer created: device={scorer.device}")
        
        # Test with sample data
        sample_segments = [
            {
                'id': 0,
                'start': 0.0,
                'end': 5.0,
                'text': 'This is absolutely incredible and amazing!'
            },
            {
                'id': 1,
                'start': 5.0,
                'end': 10.0,
                'text': 'What do you think about this approach?'
            }
        ]
        
        keywords = ['incredible', 'amazing', 'approach']
        
        print("Scoring sample segments...")
        scores = scorer.score_segments(sample_segments, keywords)
        print(f"✅ Scored {len(scores)} segments successfully")
        
        for i, score in enumerate(scores):
            print(f"   Segment {i+1}: confidence={score.confidence_score:.3f}, "
                  f"emotion={score.emotion_score:.3f}, "
                  f"rhetorical={score.rhetorical_score:.3f}")
        
        # Test performance metrics
        metrics = scorer.get_performance_metrics()
        print(f"✅ Performance metrics: {metrics['total_segments_processed']} segments processed")
        
    except Exception as e:
        print(f"⚠️  EngagementScorer test failed: {e}")
        print("This is expected if ML dependencies are not available")
    
    print("\n🎉 All direct import tests passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
