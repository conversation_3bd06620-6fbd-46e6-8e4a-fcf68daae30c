#!/usr/bin/env python3
"""
Test Suite for Enhanced Engagement Scoring System

Comprehensive tests for the sophisticated engagement scoring functionality.
"""

import os
import sys
import unittest
import json
import tempfile
from unittest.mock import Mock, patch
from typing import Dict, Any, List

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Test imports with graceful fallback
try:
    from highlight_extraction.models.engagement_models import (
        ScoringConfig, EngagementScore, EngagementType, ProcessingMode
    )
    from highlight_extraction.core.engagement_scorer import EngagementScorer
    from highlight_extraction.core.enhanced_intelligent_extractor import EnhancedIntelligentExtractor
    from highlight_extraction.pipeline.engagement_highlights_task import EngagementHighlightsTask
    ENHANCED_SCORING_AVAILABLE = True
except ImportError as e:
    print(f"Enhanced scoring not available: {e}")
    ENHANCED_SCORING_AVAILABLE = False


class TestEngagementModels(unittest.TestCase):
    """Test engagement scoring data models"""
    
    @unittest.skipUnless(ENHANCED_SCORING_AVAILABLE, "Enhanced scoring not available")
    def test_engagement_score_creation(self):
        """Test EngagementScore creation and validation"""
        score = EngagementScore(
            start_time=10.0,
            end_time=15.0,
            engagement_type=EngagementType.EMOTION.value,
            confidence_score=0.85,
            emotion_score=0.9,
            text="This is an exciting moment!"
        )
        
        self.assertEqual(score.start_time, 10.0)
        self.assertEqual(score.end_time, 15.0)
        self.assertEqual(score.duration, 5.0)
        self.assertEqual(score.confidence_score, 0.85)
        self.assertEqual(score.emotion_score, 0.9)
        self.assertEqual(score.engagement_type, "emotion")
    
    @unittest.skipUnless(ENHANCED_SCORING_AVAILABLE, "Enhanced scoring not available")
    def test_engagement_score_validation(self):
        """Test score validation and normalization"""
        score = EngagementScore(
            start_time=0.0,
            end_time=5.0,
            engagement_type="composite",
            confidence_score=1.5,  # Should be clamped to 1.0
            emotion_score=-0.1,    # Should be clamped to 0.0
        )
        
        self.assertEqual(score.confidence_score, 1.0)
        self.assertEqual(score.emotion_score, 0.0)
    
    @unittest.skipUnless(ENHANCED_SCORING_AVAILABLE, "Enhanced scoring not available")
    def test_scoring_config_from_env(self):
        """Test ScoringConfig creation from environment"""
        with patch.dict(os.environ, {
            'ENGAGEMENT_SCORING_USE_GPU': 'false',
            'ENGAGEMENT_SCORING_BATCH_SIZE': '64',
            'ENGAGEMENT_EMOTION_WEIGHT': '0.4'
        }):
            config = ScoringConfig.from_env()
            self.assertFalse(config.use_gpu)
            self.assertEqual(config.batch_size, 64)
            self.assertEqual(config.emotion_weight, 0.4)


class TestEngagementScorer(unittest.TestCase):
    """Test the EngagementScorer class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.sample_segments = [
            {
                'id': 0,
                'start': 0.0,
                'end': 5.0,
                'text': 'Hello everyone! Welcome to this amazing presentation.'
            },
            {
                'id': 1,
                'start': 5.0,
                'end': 10.0,
                'text': 'Today we will discuss some very important topics.'
            },
            {
                'id': 2,
                'start': 10.0,
                'end': 15.0,
                'text': 'What do you think about this approach? Is it effective?'
            }
        ]
        
        self.sample_keywords = ['presentation', 'important', 'effective']
    
    @unittest.skipUnless(ENHANCED_SCORING_AVAILABLE, "Enhanced scoring not available")
    def test_scorer_initialization(self):
        """Test EngagementScorer initialization"""
        config = ScoringConfig(use_gpu=False)  # Force CPU for testing
        scorer = EngagementScorer(config)
        
        self.assertIsNotNone(scorer)
        self.assertEqual(scorer.device, "cpu")
        self.assertEqual(scorer.processing_mode, ProcessingMode.CPU_FALLBACK)
    
    @unittest.skipUnless(ENHANCED_SCORING_AVAILABLE, "Enhanced scoring not available")
    def test_segment_filtering(self):
        """Test segment filtering logic"""
        config = ScoringConfig(
            use_gpu=False,
            min_segment_duration=3.0,
            max_segment_duration=20.0
        )
        scorer = EngagementScorer(config)
        
        # Add a segment that's too short
        test_segments = self.sample_segments + [{
            'id': 3,
            'start': 15.0,
            'end': 16.0,  # Only 1 second duration
            'text': 'Short.'
        }]
        
        filtered = scorer._filter_segments(test_segments)
        self.assertEqual(len(filtered), 3)  # Should exclude the short segment
    
    @unittest.skipUnless(ENHANCED_SCORING_AVAILABLE, "Enhanced scoring not available")
    @patch('highlight_extraction.core.engagement_scorer.TRANSFORMERS_AVAILABLE', False)
    def test_scorer_fallback_without_models(self):
        """Test scorer behavior when ML models are not available"""
        config = ScoringConfig(use_gpu=False)
        scorer = EngagementScorer(config)
        
        # Should still work with basic scoring
        scores = scorer.score_segments(self.sample_segments, self.sample_keywords)
        self.assertIsInstance(scores, list)
    
    @unittest.skipUnless(ENHANCED_SCORING_AVAILABLE, "Enhanced scoring not available")
    def test_cache_functionality(self):
        """Test caching mechanism"""
        config = ScoringConfig(use_gpu=False, enable_caching=True)
        scorer = EngagementScorer(config)
        
        # First call should miss cache
        scores1 = scorer.score_segments(self.sample_segments[:1], self.sample_keywords)
        
        # Second call with same data should hit cache
        scores2 = scorer.score_segments(self.sample_segments[:1], self.sample_keywords)
        
        # Should have cache hits
        metrics = scorer.get_performance_metrics()
        self.assertGreater(metrics['cache_hits'], 0)
    
    @unittest.skipUnless(ENHANCED_SCORING_AVAILABLE, "Enhanced scoring not available")
    def test_performance_metrics(self):
        """Test performance metrics collection"""
        config = ScoringConfig(use_gpu=False)
        scorer = EngagementScorer(config)
        
        scorer.score_segments(self.sample_segments, self.sample_keywords)
        
        metrics = scorer.get_performance_metrics()
        self.assertIn('total_segments_processed', metrics)
        self.assertIn('total_processing_time', metrics)
        self.assertIn('device_used', metrics)
        self.assertEqual(metrics['device_used'], 'cpu')


class TestEnhancedIntelligentExtractor(unittest.TestCase):
    """Test the EnhancedIntelligentExtractor class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.sample_transcription = {
            'duration': 30.0,
            'language': 'english',
            'segments': [
                {
                    'id': 0,
                    'start': 0.0,
                    'end': 10.0,
                    'text': 'Welcome to this incredible presentation about AI!'
                },
                {
                    'id': 1,
                    'start': 10.0,
                    'end': 20.0,
                    'text': 'What makes artificial intelligence so fascinating?'
                },
                {
                    'id': 2,
                    'start': 20.0,
                    'end': 30.0,
                    'text': 'Let me show you some amazing examples.'
                }
            ]
        }
        
        self.sample_keywords = ['AI', 'artificial intelligence', 'examples']
    
    @unittest.skipUnless(ENHANCED_SCORING_AVAILABLE, "Enhanced scoring not available")
    def test_extractor_initialization(self):
        """Test EnhancedIntelligentExtractor initialization"""
        config = ScoringConfig(use_gpu=False)
        extractor = EnhancedIntelligentExtractor(config)
        
        self.assertIsNotNone(extractor)
        self.assertTrue(extractor.is_enhanced_scoring_available())
    
    @unittest.skipUnless(ENHANCED_SCORING_AVAILABLE, "Enhanced scoring not available")
    def test_engagement_score_conversion(self):
        """Test conversion of engagement scores to highlights format"""
        config = ScoringConfig(use_gpu=False)
        extractor = EnhancedIntelligentExtractor(config)
        
        # Create mock engagement scores
        engagement_scores = [
            EngagementScore(
                start_time=0.0,
                end_time=10.0,
                engagement_type="composite",
                confidence_score=0.8,
                composite_score=0.75,
                text="Sample text"
            )
        ]
        
        highlights = extractor._convert_engagement_scores_to_highlights(engagement_scores, 75)
        
        self.assertEqual(len(highlights), 1)
        self.assertEqual(highlights[0]['start_time'], 0.0)
        self.assertEqual(highlights[0]['end_time'], 10.0)
        self.assertEqual(highlights[0]['score'], 0.8)


class TestEngagementHighlightsTask(unittest.TestCase):
    """Test the EngagementHighlightsTask pipeline integration"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        
        # Create sample transcript file
        self.transcript_data = {
            'duration': 60.0,
            'language': 'english',
            'segments': [
                {
                    'id': 0,
                    'start': 0.0,
                    'end': 15.0,
                    'text': 'This is an exciting introduction to our topic!'
                },
                {
                    'id': 1,
                    'start': 15.0,
                    'end': 30.0,
                    'text': 'What are the key benefits of this approach?'
                }
            ]
        }
        
        self.transcript_path = os.path.join(self.temp_dir, 'transcript.json')
        with open(self.transcript_path, 'w') as f:
            json.dump(self.transcript_data, f)
    
    def tearDown(self):
        """Clean up test fixtures"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @unittest.skipUnless(ENHANCED_SCORING_AVAILABLE, "Enhanced scoring not available")
    def test_task_initialization(self):
        """Test EngagementHighlightsTask initialization"""
        config = ScoringConfig(use_gpu=False)
        task = EngagementHighlightsTask(config)
        
        self.assertEqual(task.task_name, "engagement_highlights")
        self.assertTrue(task.requires_gpu)
    
    @unittest.skipUnless(ENHANCED_SCORING_AVAILABLE, "Enhanced scoring not available")
    def test_task_info(self):
        """Test task information retrieval"""
        config = ScoringConfig(use_gpu=False)
        task = EngagementHighlightsTask(config)
        
        info = task.get_task_info()
        self.assertIn('task_name', info)
        self.assertIn('engagement_scoring_enabled', info)
        self.assertIn('config_summary', info)
    
    @unittest.skipUnless(ENHANCED_SCORING_AVAILABLE, "Enhanced scoring not available")
    def test_dependency_validation(self):
        """Test dependency validation"""
        config = ScoringConfig(use_gpu=False)
        task = EngagementHighlightsTask(config)
        
        dependencies = task.validate_dependencies()
        self.assertIn('numpy', dependencies)
        self.assertIn('sklearn', dependencies)
        self.assertIsInstance(dependencies['numpy'], bool)


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete engagement scoring system"""
    
    @unittest.skipUnless(ENHANCED_SCORING_AVAILABLE, "Enhanced scoring not available")
    def test_end_to_end_scoring(self):
        """Test complete end-to-end engagement scoring"""
        # Create test data
        segments = [
            {
                'id': 0,
                'start': 0.0,
                'end': 8.0,
                'text': 'Welcome everyone! This is going to be absolutely amazing!'
            },
            {
                'id': 1,
                'start': 8.0,
                'end': 16.0,
                'text': 'What do you think about this revolutionary approach?'
            },
            {
                'id': 2,
                'start': 16.0,
                'end': 24.0,
                'text': 'Let me explain the technical details step by step.'
            }
        ]
        
        keywords = ['amazing', 'revolutionary', 'technical']
        
        # Initialize scorer with CPU-only config for testing
        config = ScoringConfig(use_gpu=False, batch_size=2)
        scorer = EngagementScorer(config)
        
        # Score segments
        engagement_scores = scorer.score_segments(segments, keywords)
        
        # Validate results
        self.assertIsInstance(engagement_scores, list)
        self.assertGreater(len(engagement_scores), 0)
        
        for score in engagement_scores:
            self.assertIsInstance(score, EngagementScore)
            self.assertGreaterEqual(score.confidence_score, 0.0)
            self.assertLessEqual(score.confidence_score, 1.0)
            self.assertGreater(score.duration, 0.0)
        
        # Test analysis
        analysis = scorer.analyze_engagement_distribution(engagement_scores)
        self.assertIn('total_segments', analysis)
        self.assertIn('score_distribution', analysis)
        
        # Test performance metrics
        metrics = scorer.get_performance_metrics()
        self.assertIn('total_segments_processed', metrics)
        self.assertGreater(metrics['total_segments_processed'], 0)


if __name__ == '__main__':
    # Configure logging for tests
    import logging
    logging.basicConfig(level=logging.WARNING)
    
    # Run tests
    unittest.main(verbosity=2)
