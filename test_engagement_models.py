#!/usr/bin/env python3

print("Testing engagement models...")

try:
    # Test individual imports
    print("Importing EngagementType...")
    from highlight_extraction.models.engagement_models import EngagementType
    print("✅ EngagementType imported")
    
    print("Importing ProcessingMode...")
    from highlight_extraction.models.engagement_models import ProcessingMode
    print("✅ ProcessingMode imported")
    
    print("Importing ScoringConfig...")
    from highlight_extraction.models.engagement_models import ScoringConfig
    print("✅ ScoringConfig imported")
    
    print("Importing EngagementScore...")
    from highlight_extraction.models.engagement_models import EngagementScore
    print("✅ EngagementScore imported")
    
    # Test enum usage
    print("Testing enum usage...")
    emotion_type = EngagementType.EMOTION
    print(f"✅ EngagementType.EMOTION = {emotion_type.value}")
    
    mode = ProcessingMode.CPU_FALLBACK
    print(f"✅ ProcessingMode.CPU_FALLBACK = {mode.value}")
    
    # Test config creation
    print("Testing ScoringConfig creation...")
    config = ScoringConfig(use_gpu=False, batch_size=16)
    print(f"✅ ScoringConfig created: GPU={config.use_gpu}, batch={config.batch_size}")
    
    # Test score creation
    print("Testing EngagementScore creation...")
    score = EngagementScore(
        start_time=0.0,
        end_time=5.0,
        engagement_type="emotion",
        confidence_score=0.85
    )
    print(f"✅ EngagementScore created: {score.start_time}s-{score.end_time}s, confidence={score.confidence_score}")
    print(f"   Duration: {score.duration}s")
    
    # Test serialization
    print("Testing serialization...")
    score_dict = score.to_dict()
    print(f"✅ Score serialized to dict with {len(score_dict)} keys")
    
    config_dict = config.to_dict()
    print(f"✅ Config serialized to dict with {len(config_dict)} keys")
    
    print("🎉 All engagement model tests passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
